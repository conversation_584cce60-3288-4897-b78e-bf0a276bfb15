#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试评估脚本
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

from biggoal_evaluation import load_detection_results, load_ground_truth, evaluate_detection_results
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_evaluation():
    """测试评估功能"""
    
    # 测试参数
    detection_results_file = "Output/results/shrinked_results.json"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/big_spot_frames"
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    class_json = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json"
    sample_ratio = 0.05
    
    try:
        # 1. 加载检测结果
        logger.info("加载检测结果...")
        detection_results = load_detection_results(detection_results_file)
        logger.info(f"加载了 {len(detection_results)} 个检测结果")
        
        if detection_results:
            pred_sequences = set(r.sequence_id for r in detection_results)
            logger.info(f"检测结果中的序列: {pred_sequences}")
            logger.info(f"检测结果示例: {detection_results[0].__dict__}")
        
        # 2. 加载标注数据
        logger.info("加载标注数据...")
        target_sequences = list(set(r.sequence_id for r in detection_results)) if detection_results else []
        logger.info(f"目标序列: {target_sequences}")
        
        ground_truth = load_ground_truth(
            annotation_path, class_json, data_path, 
            sample_ratio, target_sequences
        )
        
        gt_sequences = set(ground_truth.keys())
        logger.info(f"标注数据中的序列: {gt_sequences}")
        total_gt_count = sum(len(gts) for gts in ground_truth.values())
        logger.info(f"标注数据总数: {total_gt_count}")
        
        # 3. 检查序列匹配
        common_sequences = pred_sequences & gt_sequences
        logger.info(f"共同序列: {common_sequences}")
        
        if not common_sequences:
            logger.error("没有找到共同的序列！检查序列ID是否匹配")
            return False
        
        # 4. 执行评估
        logger.info("开始评估...")
        eval_results = evaluate_detection_results(detection_results, ground_truth)
        
        # 5. 输出结果
        logger.info("=== 评估结果 ===")
        logger.info(f"精确率: {eval_results['precision']:.4f}")
        logger.info(f"召回率: {eval_results['recall']:.4f}")
        logger.info(f"F1分数: {eval_results['f1_score']:.4f}")
        logger.info(f"时序一致性: {eval_results['temporal_consistency']:.4f}")
        logger.info(f"高一致性序列数: {eval_results['high_consistency_sequences']}/{eval_results['total_sequences']}")
        
        return True
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_evaluation()
    if success:
        print("✅ 测试成功")
    else:
        print("❌ 测试失败")
        sys.exit(1)
