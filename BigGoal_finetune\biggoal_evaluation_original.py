#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BigGoal弱小目标检测评估系统
完全按照原始代码逻辑进行评估
"""

import os
import re
import json
import numpy as np
from PIL import Image
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from collections import defaultdict
import logging
from tqdm import tqdm
import argparse
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def natural_sort_key(filename: str) -> List:
    """自然排序键函数"""
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return [int(numbers[-1])]
    else:
        return [filename.lower()]

def extract_frame_number(filename: str) -> int:
    """从文件名中提取帧序号"""
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return int(numbers[-1])
    else:
        return 0

@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: List[float]  # [x1, y1, x2, y2]
    label: str
    confidence: float
    frame_id: str
    sequence_id: str
    temporal_score: float = 0.5

@dataclass
class GroundTruth:
    """真实标注数据类"""
    bbox: List[float]  # [x1, y1, x2, y2]
    label: str
    frame_id: str
    sequence_id: str

def load_class_map(class_json_path: str):
    """加载类别映射"""
    with open(class_json_path, 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    # 反向映射
    class_map_rev = {v: v for v in class_map.values()}
    zh2en = {"无人机": "drone", "汽车": "car", "轮船": "ship", "公交车": "bus", "行人": "pedestrian", "骑行者": "cyclist"}
    class_map_rev.update(zh2en)
    return class_map, class_map_rev

def load_ground_truth(labels_root: str, class_json_path: str, img_root: str, 
                     sample_ratio: float = 0.2, target_sequences: list = None):
    """加载标注数据"""
    class_map, _ = load_class_map(class_json_path)
    ground_truth = defaultdict(list)
    
    target_sequences = target_sequences or ["data03", "data05", "data20", "data21", "data22"]
    
    for seq in sorted(os.listdir(labels_root)):
        if seq not in target_sequences:
            continue
            
        seq_label_path = os.path.join(labels_root, seq)
        seq_img_path = os.path.join(img_root, seq)
        
        if not os.path.isdir(seq_label_path):
            continue
        
        # 获取标注文件
        label_files = [f for f in os.listdir(seq_label_path) if f.endswith('.txt')]
        label_files.sort(key=natural_sort_key)
        
        # 采样
        num_files = len(label_files)
        num_to_process = max(1, int(num_files * sample_ratio))
        selected_files = label_files[:num_to_process]
        
        logger.info(f"序列 {seq} 标注: 共 {num_files} 个文件，处理前 {num_to_process} 个 ({sample_ratio*100:.2f}%)")
        
        for label_file in selected_files:
            label_path = os.path.join(seq_label_path, label_file)
            frame_id = str(extract_frame_number(label_file))
            
            # 获取图像尺寸
            img_file = label_file.replace('.txt', '.jpg')
            img_path = os.path.join(seq_img_path, img_file)
            
            if not os.path.exists(img_path):
                continue
            
            try:
                with Image.open(img_path) as img:
                    w, h = img.size
            except:
                w, h = 640, 512
            
            # 解析标注
            with open(label_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    
                    cls_id, cx, cy, bw, bh = parts[:5]
                    label = class_map.get(str(cls_id), 'unknown')
                    cx, cy, bw, bh = map(float, [cx, cy, bw, bh])
                    
                    # 转换为绝对坐标
                    x1 = (cx - bw/2) * w
                    y1 = (cy - bh/2) * h
                    x2 = (cx + bw/2) * w
                    y2 = (cy + bh/2) * h
                    
                    gt = GroundTruth(
                        bbox=[x1, y1, x2, y2],
                        label=label,
                        frame_id=frame_id,
                        sequence_id=seq
                    )
                    ground_truth[seq].append(gt)
    
    return ground_truth

def calculate_iou(bbox1: List[float], bbox2: List[float]) -> float:
    """计算IoU"""
    x1_1, y1_1, x2_1, y2_1 = bbox1
    x1_2, y1_2, x2_2, y2_2 = bbox2
    
    # 计算交集
    x1_i = max(x1_1, x1_2)
    y1_i = max(y1_1, y1_2)
    x2_i = min(x2_1, x2_2)
    y2_i = min(y2_1, y2_2)
    
    if x2_i <= x1_i or y2_i <= y1_i:
        return 0.0
    
    intersection = (x2_i - x1_i) * (y2_i - y1_i)
    
    # 计算并集
    area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
    area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
    union = area1 + area2 - intersection
    
    return intersection / union if union > 0 else 0.0

def evaluate_detection_results(predictions: List[DetectionResult], ground_truth: Dict, 
                             iou_threshold: float = 0.3) -> Dict:
    """评估检测结果"""
    # 按序列和帧分组
    pred_by_seq_frame = defaultdict(lambda: defaultdict(list))
    gt_by_seq_frame = defaultdict(lambda: defaultdict(list))
    
    for pred in predictions:
        pred_by_seq_frame[pred.sequence_id][pred.frame_id].append(pred)
    
    for seq_id, gts in ground_truth.items():
        for gt in gts:
            gt_by_seq_frame[seq_id][gt.frame_id].append(gt)
    
    # 计算指标
    total_tp = 0
    total_fp = 0
    total_fn = 0
    temporal_consistency_scores = []
    
    common_sequences = set(pred_by_seq_frame.keys()) & set(gt_by_seq_frame.keys())
    
    for seq_id in tqdm(common_sequences, desc="评估序列"):
        all_frames = set(pred_by_seq_frame[seq_id].keys()) | set(gt_by_seq_frame[seq_id].keys())
        
        for frame_id in all_frames:
            frame_preds = pred_by_seq_frame[seq_id][frame_id]
            frame_gts = gt_by_seq_frame[seq_id][frame_id]
            
            # 匹配预测和真值
            matched_preds = set()
            matched_gts = set()
            
            for i, pred in enumerate(frame_preds):
                for j, gt in enumerate(frame_gts):
                    if j in matched_gts:
                        continue
                    
                    iou = calculate_iou(pred.bbox, gt.bbox)
                    if iou >= iou_threshold and pred.label == gt.label:
                        matched_preds.add(i)
                        matched_gts.add(j)
                        break
            
            # 统计TP, FP, FN
            tp = len(matched_preds)
            fp = len(frame_preds) - tp
            fn = len(frame_gts) - len(matched_gts)
            
            total_tp += tp
            total_fp += fp
            total_fn += fn
            
            # 时序一致性（简化版）
            if frame_preds:
                temporal_consistency_scores.append(np.mean([p.temporal_score for p in frame_preds]))
    
    # 计算最终指标
    precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
    recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
    f1_score = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0.0
    
    temporal_consistency = np.mean(temporal_consistency_scores) if temporal_consistency_scores else 0.0
    high_consistency_seqs = sum(1 for score in temporal_consistency_scores if score > 0.8)
    
    results = {
        'precision': precision,
        'recall': recall,
        'f1_score': f1_score,
        'temporal_consistency': temporal_consistency,
        'high_consistency_sequences': high_consistency_seqs,
        'total_sequences': len(common_sequences)
    }
    
    return results

def main():
    """主函数 - 完全按照原始代码逻辑"""
    parser = argparse.ArgumentParser(description='BigGoal弱小目标检测评估')
    parser.add_argument('--detection_results', type=str, required=True, help='检测结果文件路径')
    parser.add_argument('--data_path', type=str, required=True, help='图像数据路径')
    parser.add_argument('--annotation_path', type=str, required=True, help='标注数据路径')
    parser.add_argument('--class_json', type=str, required=True, help='类别映射文件')
    parser.add_argument('--output_dir', type=str, default='output', help='输出目录')
    parser.add_argument('--sample_ratio', type=float, default=0.2, help='采样比例')
    
    args = parser.parse_args()
    
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 加载检测结果（转换为DetectionResult格式）
    with open(args.detection_results, 'r', encoding='utf-8') as f:
        detection_data = json.load(f)
    
    detection_results = []
    for item in detection_data:
        result = DetectionResult(
            bbox=item['bbox'],
            label=item['label'],
            confidence=item['confidence'],
            frame_id=item['frame_id'],
            sequence_id=item['sequence_id'],
            temporal_score=item.get('temporal_score', 0.5)
        )
        detection_results.append(result)
    
    # 从检测结果中获取目标序列（这是关键修改）
    target_sequences = list(set(r.sequence_id for r in detection_results))
    
    # 加载标注数据并评估
    logger.info("开始评估检测结果")
    ground_truth = load_ground_truth(
        args.annotation_path, args.class_json, args.data_path, 
        args.sample_ratio, target_sequences
    )
    
    # 评估
    eval_results = evaluate_detection_results(detection_results, ground_truth)
    
    # 保存评估结果
    eval_file = os.path.join(args.output_dir, "biggoal_evaluation_results.json")
    with open(eval_file, 'w', encoding='utf-8') as f:
        json.dump(eval_results, f, ensure_ascii=False, indent=2)
    
    # 输出结果
    logger.info("=== 评估结果 ===")
    logger.info(f"目标识别精度 - 平均召回率: {eval_results['recall']:.4f}")
    logger.info(f"目标识别精度 - 平均精确率: {eval_results['precision']:.4f}")
    logger.info(f"目标识别精度 - F1分数: {eval_results['f1_score']:.4f}")
    logger.info(f"时空序列稳定性: {eval_results['temporal_consistency']:.4f}")
    logger.info(f"时序一致性超过80%的序列数: {eval_results['high_consistency_sequences']}/{eval_results['total_sequences']}")
    
    if eval_results['precision'] < 0.5:
        logger.warning("⚠️ 评估精确率不足，建议调整检测参数")
    
    logger.info(f"评估结果已保存到: {eval_file}")

if __name__ == "__main__":
    main()
