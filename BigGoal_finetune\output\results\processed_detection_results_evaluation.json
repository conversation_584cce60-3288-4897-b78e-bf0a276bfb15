{"overall_precision": 0.5086705202312138, "overall_recall": 0.4656084656084656, "overall_f1_score": 0.4861878453038674, "temporal_stability": 0.2, "consistent_sequences_ratio": 0.2, "total_sequences": 5, "consistent_sequences": 1, "sequence_results": {"data05": {"precision": 0.0, "recall": 0.0, "f1_score": 0, "temporal_consistency": 0.0, "total_frames": 19, "matched_frames": 0, "total_tp": 0, "total_fp": 7, "total_fn": 19}, "data20": {"precision": 0.3, "recall": 0.2876712328767123, "f1_score": 0.2937062937062937, "temporal_consistency": 0.2876712328767123, "total_frames": 73, "matched_frames": 21, "total_tp": 21, "total_fp": 49, "total_fn": 52}, "data21": {"precision": 0.86, "recall": 0.86, "f1_score": 0.8599999999999999, "temporal_consistency": 0.86, "total_frames": 50, "matched_frames": 43, "total_tp": 43, "total_fp": 7, "total_fn": 7}, "data22": {"precision": 0.5217391304347826, "recall": 0.5106382978723404, "f1_score": 0.5161290322580645, "temporal_consistency": 0.5106382978723404, "total_frames": 47, "matched_frames": 24, "total_tp": 24, "total_fp": 22, "total_fn": 23}}}