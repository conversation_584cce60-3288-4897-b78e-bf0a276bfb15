# 坐标修正版检测评估系统

## 🎯 问题背景

在使用Qwen2.5-VL模型进行目标检测时，发现了一个重要问题：

### 问题现象
- **28的倍数尺寸图像**（如560×560、672×448）：模型输出的绝对坐标能够准确对应到原始图像中的目标位置
- **非28倍数尺寸图像**（如640×512、256×256）：输出的坐标出现偏移，无法准确定位目标

### 问题根源
Qwen2.5-VL模型的图像预处理机制：
1. **默认尺寸限制**：模型设定了max_pixels默认为1280×28×28（约1000×1000像素）
2. **自动调整机制**：当输入图像超过限制时，模型会自动resize以适配处理要求
3. **坐标映射关系**：模型输出的坐标是基于调整后的图像尺寸，而非原始图像尺寸

### 技术原理
- **28的倍数要求**：模型要求输入图像尺寸最好是28的倍数
- **保持长宽比**：调整时保持原始图像的长宽比不变
- **智能调整策略**：在min_pixels和max_pixels范围内进行优化调整

## 🔧 解决方案

### 核心修正功能
`evaluate_finetuned_bbox_corrected.py` 实现了完整的坐标修正系统：

1. **智能尺寸计算**：使用官方的`smart_resize`函数计算模型内部调整后的图像尺寸
2. **双向坐标转换**：
   - `convert_bbox_to_original_coords`：将模型输出坐标转换回原始图像坐标
   - `convert_bbox_to_model_coords`：将原始标注坐标转换为模型内部坐标
3. **自动修正流程**：检测过程中自动进行坐标转换，确保结果准确

### 修正算法
```python
def smart_resize(height, width, factor=28, min_pixels=56*56, max_pixels=14*14*4*1280):
    # 确保尺寸是28的倍数
    h_bar = round(height / factor) * factor
    w_bar = round(width / factor) * factor
    
    # 控制像素范围
    if h_bar * w_bar > max_pixels:
        beta = math.sqrt((height * width) / max_pixels)
        h_bar = math.floor(height / beta / factor) * factor
        w_bar = math.floor(width / beta / factor) * factor
    elif h_bar * w_bar < min_pixels:
        beta = math.sqrt(min_pixels / (height * width))
        h_bar = math.ceil(height * beta / factor) * factor
        w_bar = math.ceil(width * beta / factor) * factor
    
    return h_bar, w_bar
```

## 🚀 使用方法

### 基本用法
```bash
# 在Qwen虚拟环境中运行
CUDA_VISIBLE_DEVICES=0 python evaluate_finetuned_bbox_corrected.py \
    --base_model_path "/home/<USER>/Qwen/Qwen2.5-VL/Qwen2.5-VL-7B-Instruct" \
    --lora_model_path "/home/<USER>/Qwen/Qwen2.5-VL/weak_target_finetune/output/weak_target_lora/final" \
    --data_path "/home/<USER>/Qwen/Qwen2.5-VL/new_processed_data" \
    --annotation_path "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/labels" \
    --output_path "./Output/detection_results_bbox_corrected.json" \
    --class_json "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json" \
    --sample_ratio 0.05 \
    --sequences data03 data05 data20 data21 data22
```

### 参数说明
- `--base_model_path`: 基础Qwen2.5-VL模型路径（必需）
- `--lora_model_path`: LoRA微调模型路径（可选）
- `--data_path`: 测试数据集路径（必需）
- `--annotation_path`: 真实标注文件路径（可选，用于评估）
- `--output_path`: 输出结果路径（默认包含bbox_corrected标识）
- `--sample_ratio`: 采样比例（默认0.01）
- `--sequences`: 指定检测序列（可选）

## 📊 输出格式

### 检测结果JSON
```json
[
  {
    "sequence_id": "data03",
    "frame_id": "0",
    "bbox": [100, 100, 200, 200],           // 修正后的坐标（原始图像坐标系）
    "label": "drone",
    "confidence": 0.85,
    "temporal_score": 0.8,
    "original_bbox": [95, 98, 195, 198],    // 模型原始输出坐标
    "image_size": [1920, 1080],             // 原始图像尺寸
    "coordinate_corrected": true            // 是否进行了坐标修正
  }
]
```

### 运行输出示例
```
🔧 红外弱小目标检测系统 - 坐标修正版
============================================================
✨ 新功能: 自动修正Qwen2.5-VL模型的坐标映射问题
📋 解决问题: 模型输出坐标与原始图像坐标不匹配
🎯 适用场景: 非28倍数尺寸图像（如640×512、256×256）的精确目标定位
============================================================

🔧 已启用坐标修正功能，将自动处理Qwen2.5-VL的图像尺寸调整问题
🚀 开始检测 5 个序列，每个序列处理前 5.00% 的数据...
🔧 使用坐标修正功能，确保检测框准确对应原始图像位置

坐标修正统计: 245/256 个检测结果进行了坐标修正
```

## 🔍 技术细节

### 坐标转换流程
1. **检测阶段**：
   - 获取原始图像尺寸
   - 模型自动调整图像尺寸
   - 模型输出基于调整后尺寸的坐标
   - 自动转换为原始图像坐标

2. **评估阶段**：
   - 将标注坐标转换为模型内部坐标系
   - 确保检测结果和标注在同一坐标系下比较
   - 计算准确的IoU和其他指标

### 关键优势
- **自动化处理**：无需手动调整，自动识别和修正坐标偏移
- **保持兼容性**：与原有评估流程完全兼容
- **详细记录**：保存原始坐标和修正坐标，便于分析
- **精确定位**：确保检测框在原始图像上的准确位置

## 📈 效果对比

### 修正前
- 非28倍数尺寸图像检测框位置偏移
- IoU计算不准确
- 评估指标失真

### 修正后
- 检测框准确对应原始图像位置
- IoU计算基于正确的坐标系
- 评估指标真实反映模型性能

## 🛠️ 故障排除

### 常见问题
1. **坐标转换失败**：
   - 检查图像文件是否损坏
   - 确认图像尺寸获取正确

2. **修正效果不明显**：
   - 小尺寸图像本身不需要修正
   - 检查是否为28的倍数尺寸

3. **性能影响**：
   - 坐标转换计算量很小，对性能影响微乎其微

### 调试技巧
```bash
# 启用详细日志查看坐标转换过程
python evaluate_finetuned_bbox_corrected.py --verbose ...
```

## 📝 注意事项

1. **适用场景**：主要解决大尺寸图像的坐标偏移问题
2. **兼容性**：完全兼容原有的检测和评估流程
3. **性能**：坐标转换开销极小，不影响检测速度
4. **准确性**：基于官方提供的resize算法，确保转换准确

## 🎯 推荐使用

建议在以下情况使用坐标修正版：
- 处理非28倍数尺寸图像（如640×512、256×256等）
- 需要精确的目标定位
- 进行定量评估和分析
- 对检测精度要求较高的应用

这个修正版本解决了Qwen2.5-VL模型在处理不同尺寸图像时的坐标映射问题，确保检测结果的准确性和可靠性。
