#!/bin/bash

echo "BigGoal弱小目标检测和评估系统"
echo "================================"

# 设置默认参数（用户需要根据实际情况修改）
MODEL_PATH="./models/qwen2.5-vl-finetuned"
DATA_PATH="./data/images"
ANNOTATION_PATH="./data/labels"
CLASS_JSON="./data/class_mapping.json"
OUTPUT_DIR="./output"
SAMPLE_RATIO="0.2"

echo "当前配置:"
echo "模型路径: $MODEL_PATH"
echo "数据路径: $DATA_PATH"
echo "标注路径: $ANNOTATION_PATH"
echo "类别映射: $CLASS_JSON"
echo "输出目录: $OUTPUT_DIR"
echo "采样比例: $SAMPLE_RATIO"
echo

echo "开始执行检测和评估..."
python run_detection_evaluation.py \
    --model_path "$MODEL_PATH" \
    --data_path "$DATA_PATH" \
    --annotation_path "$ANNOTATION_PATH" \
    --class_json "$CLASS_JSON" \
    --output_dir "$OUTPUT_DIR" \
    --sample_ratio "$SAMPLE_RATIO"

if [ $? -eq 0 ]; then
    echo
    echo "✅ 执行完成！"
    echo "检测结果: $OUTPUT_DIR/biggoal_detection_results.json"
    echo "评估结果: $OUTPUT_DIR/biggoal_evaluation_results.json"
else
    echo
    echo "❌ 执行失败，请检查错误信息"
    exit 1
fi
