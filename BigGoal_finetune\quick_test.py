#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试评估功能
"""

import json
import os
from collections import defaultdict

def quick_test():
    """快速测试"""
    
    # 检查检测结果文件
    detection_file = "Output/results/shrinked_results.json"
    if not os.path.exists(detection_file):
        print(f"❌ 检测结果文件不存在: {detection_file}")
        return False
    
    # 加载检测结果
    with open(detection_file, 'r', encoding='utf-8') as f:
        detection_data = json.load(f)
    
    print(f"✅ 检测结果文件存在，包含 {len(detection_data)} 个检测结果")
    
    # 分析序列
    sequences = defaultdict(int)
    for item in detection_data:
        seq_id = item.get('sequence_id', 'unknown')
        sequences[seq_id] += 1
    
    print(f"检测结果中的序列:")
    for seq, count in sequences.items():
        print(f"  {seq}: {count} 个检测结果")
    
    # 检查必需字段
    if detection_data:
        first_item = detection_data[0]
        required_fields = ['bbox', 'label', 'confidence', 'frame_id', 'sequence_id']
        missing_fields = [field for field in required_fields if field not in first_item]
        
        if missing_fields:
            print(f"❌ 缺少必需字段: {missing_fields}")
            return False
        else:
            print(f"✅ 所有必需字段都存在")
    
    # 检查标注数据路径（这里只是检查路径格式，不实际访问）
    annotation_path = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets_val/labels"
    data_path = "/home/<USER>/Qwen/Qwen2.5-VL/big_spot_frames"
    class_json = "/home/<USER>/Qwen/Qwen2.5-VL/tiaozhanbei_datasets/class.json"
    
    print(f"标注路径: {annotation_path}")
    print(f"数据路径: {data_path}")
    print(f"类别文件: {class_json}")
    
    # 构建评估命令
    cmd = f"""python biggoal_evaluation.py \\
    --detection_results {detection_file} \\
    --data_path "{data_path}" \\
    --annotation_path "{annotation_path}" \\
    --class_json "{class_json}" \\
    --sample_ratio 0.05"""
    
    print(f"\n建议的评估命令:")
    print(cmd)
    
    return True

if __name__ == "__main__":
    success = quick_test()
    if success:
        print("\n✅ 快速测试通过，可以运行评估")
    else:
        print("\n❌ 快速测试失败")
