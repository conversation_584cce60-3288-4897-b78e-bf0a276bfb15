#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红外弱小目标检测系统 - 微调版 (坐标修正版)
基于evaluate_finetuned_improved.py，修正了Qwen2.5-VL模型的坐标映射问题
解决了模型输出坐标与原始图像坐标不匹配的问题
"""

import os
import re
import json
import numpy as np
from collections import defaultdict
import logging
from tqdm import tqdm
import argparse
from pathlib import Path
import sys
import math

# 设置CUDA环境，避免设备冲突
os.environ["CUDA_VISIBLE_DEVICES"] = "0"

import torch
from PIL import Image
import cv2
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass

# 添加父目录到路径，以便导入qwen_vl_utils
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from peft import PeftModel
from qwen_vl_utils import process_vision_info

# 导入评估器
try:
    from evaluate_finetuned_improved import ImprovedDetectionEvaluator
except ImportError:
    # 如果导入失败，我们需要在这里定义一个简化版本
    print("警告: 无法导入ImprovedDetectionEvaluator，将使用简化版本")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def natural_sort_key(filename: str) -> List:
    """
    自然排序键函数，正确处理数字排序
    """
    import re
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return [int(numbers[-1])]
    else:
        return [filename.lower()]

def extract_frame_number(filename: str) -> int:
    """
    从文件名中提取帧序号
    """
    import re
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return int(numbers[-1])
    else:
        return 0

# Qwen2.5-VL的图像尺寸调整函数（官方提供）
def smart_resize(
    height: int, width: int, factor: int = 28, min_pixels: int = 56 * 56, max_pixels: int = 14 * 14 * 4 * 1280
):
    """
    Rescales the image so that the following conditions are met:
    1. Both dimensions (height and width) are divisible by 'factor'.
    2. The total number of pixels is within the range ['min_pixels', 'max_pixels'].
    3. The aspect ratio of the image is maintained as closely as possible.
    """
    if height < factor or width < factor:
        raise ValueError(f"height:{height} or width:{width} must be larger than factor:{factor}")
    elif max(height, width) / min(height, width) > 200:
        raise ValueError(
            f"absolute aspect ratio must be smaller than 200, got {max(height, width) / min(height, width)}"
        )
    h_bar = round(height / factor) * factor
    w_bar = round(width / factor) * factor
    if h_bar * w_bar > max_pixels:
        beta = math.sqrt((height * width) / max_pixels)
        h_bar = math.floor(height / beta / factor) * factor
        w_bar = math.floor(width / beta / factor) * factor
    elif h_bar * w_bar < min_pixels:
        beta = math.sqrt(min_pixels / (height * width))
        h_bar = math.ceil(height * beta / factor) * factor
        w_bar = math.ceil(width * beta / factor) * factor
    return h_bar, w_bar

def convert_bbox_to_original_coords(bbox, orig_height, orig_width, factor=28, min_pixels=56*56, max_pixels=14*14*4*1280):
    """
    将模型输出的坐标转换回原始图像坐标
    
    Args:
        bbox: 模型输出的边界框坐标 [x1, y1, x2, y2]
        orig_height: 原始图像高度
        orig_width: 原始图像宽度
        factor: 调整因子（默认28）
        min_pixels: 最小像素数
        max_pixels: 最大像素数
    
    Returns:
        转换后的边界框坐标 [x1, y1, x2, y2]
    """
    # 获取模型内部调整后的尺寸
    new_height, new_width = smart_resize(orig_height, orig_width, factor, min_pixels, max_pixels)
    
    # 计算缩放比例
    scale_w = orig_width / new_width
    scale_h = orig_height / new_height
    
    # 转换坐标
    x1, y1, x2, y2 = bbox[:4]  # 只取前4个值，防止有额外的值
    
    x1_orig = round(x1 * scale_w)
    y1_orig = round(y1 * scale_h)
    x2_orig = round(x2 * scale_w)
    y2_orig = round(y2 * scale_h)
    
    # 确保坐标在原始图像范围内
    x1_orig = max(0, min(x1_orig, orig_width - 1))
    y1_orig = max(0, min(y1_orig, orig_height - 1))
    x2_orig = max(0, min(x2_orig, orig_width - 1))
    y2_orig = max(0, min(y2_orig, orig_height - 1))
    
    return [x1_orig, y1_orig, x2_orig, y2_orig]

def convert_bbox_to_model_coords(bbox, orig_height, orig_width, factor=28, min_pixels=56*56, max_pixels=14*14*4*1280):
    """
    将原始图像坐标转换为模型内部坐标（用于标注数据）
    
    Args:
        bbox: 原始图像的边界框坐标 [x1, y1, x2, y2]
        orig_height: 原始图像高度
        orig_width: 原始图像宽度
        factor: 调整因子（默认28）
        min_pixels: 最小像素数
        max_pixels: 最大像素数
    
    Returns:
        转换后的边界框坐标 [x1, y1, x2, y2]
    """
    # 获取模型内部调整后的尺寸
    new_height, new_width = smart_resize(orig_height, orig_width, factor, min_pixels, max_pixels)
    
    # 计算缩放比例
    scale_w = new_width / orig_width
    scale_h = new_height / orig_height
    
    # 转换坐标
    x1, y1, x2, y2 = bbox[:4]
    
    x1_new = round(x1 * scale_w)
    y1_new = round(y1 * scale_h)
    x2_new = round(x2 * scale_w)
    y2_new = round(y2 * scale_h)
    
    # 确保坐标在调整后图像范围内
    x1_new = max(0, min(x1_new, new_width - 1))
    y1_new = max(0, min(y1_new, new_height - 1))
    x2_new = max(0, min(x2_new, new_width - 1))
    y2_new = max(0, min(y2_new, new_height - 1))
    
    return [x1_new, y1_new, x2_new, y2_new]

@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: List[float]  # [x1, y1, x2, y2] - 原始图像坐标
    label: str
    confidence: float
    frame_id: str
    sequence_id: str
    temporal_score: float = 0.5
    original_bbox: List[float] = None  # 模型原始输出坐标
    image_size: Tuple[int, int] = None  # (width, height)

@dataclass
class GroundTruth:
    """真实标注数据类"""
    bbox: List[float]  # [x1, y1, x2, y2] - 原始图像坐标
    label: str
    frame_id: str
    sequence_id: str
    image_size: Tuple[int, int] = None  # (width, height)

class InfraredTargetDetectorCorrected:
    """红外弱小目标检测器 - 坐标修正版"""

    def __init__(self, base_model_path: str, lora_model_path: str = None, device: str = "auto", class_json_path: str = "class.json", frame_group_size: int = 5):
        """
        初始化检测器
        """
        self.device = device
        self.frame_group_size = frame_group_size
        self.lora_model_path = lora_model_path

        if lora_model_path:
            logger.info(f"正在加载基础模型: {base_model_path}")
            logger.info(f"正在加载LoRA模型: {lora_model_path}")
        else:
            logger.info(f"正在加载模型: {base_model_path}")
        logger.info(f"时间窗口大小: {frame_group_size} 帧")

        # 设置多GPU策略
        import torch
        if torch.cuda.device_count() > 1:
            logger.info(f"检测到 {torch.cuda.device_count()} 个GPU，使用多GPU加速")

        # 加载基础模型
        base_model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            base_model_path,
            torch_dtype="auto",
            device_map="auto"
        )

        # 如果有LoRA模型，则加载微调后的模型
        if lora_model_path:
            self.model = PeftModel.from_pretrained(base_model, lora_model_path)
            self.model.eval()
            logger.info("LoRA微调模型加载完成")
        else:
            self.model = base_model

        self.processor = AutoProcessor.from_pretrained(base_model_path)

        # 使用针对弱小目标优化的检测提示词
        self.detection_prompt = (
            "请识别图像中数目为1个的弱小目标，包括无人机等红外目标（这里都是无人机）；同时请重点检测每帧中的微小目标，特别是移动的白亮无人机模糊轮廓。\n"
            "检测到目标后，输出的边界框（bbox）需尽量贴合目标，精准框住目标大小，避免过大或过小。\n"
            "请严格以JSON数组格式输出目标，且label字段必须为以下英文类别之一："
            "drone, car, ship, bus, pedestrian, cyclist。\n"
            "格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]"
        )

        # 加载类别映射
        self.class_map, self.class_map_rev = load_class_map(class_json_path)

        logger.info("模型加载完成")
        logger.info("🔧 已启用坐标修正功能，将自动处理Qwen2.5-VL的图像尺寸调整问题")

    def get_sorted_image_files(self, sequence_path: str) -> List[Path]:
        """
        获取正确排序的图像文件列表
        """
        image_files = []
        for ext in ['*.bmp', '*.jpg', '*.jpeg', '*.png']:
            image_files.extend(Path(sequence_path).glob(ext))

        # 使用自然排序，确保正确的数字顺序
        image_files.sort(key=lambda x: natural_sort_key(x.name))

        return image_files

    def detect_frame_group(self, image_paths: List[str], frame_indices: List[int]) -> List[DetectionResult]:
        """
        检测帧组中的目标 - 简化版本，直接使用单帧检测但保留时序信息
        """
        results = []
        for i, (img_path, frame_idx) in enumerate(zip(image_paths, frame_indices)):
            frame_results = self.detect_single_frame(img_path)
            for result in frame_results:
                result.frame_id = str(frame_idx)
                result.temporal_score = 0.8  # 时间窗口内的检测给予更高时序得分
            results.extend(frame_results)
        return results

    def detect_single_frame(self, image_path: str) -> List[DetectionResult]:
        """
        检测单帧图像中的目标 - 坐标修正版
        """
        try:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

            # 获取原始图像尺寸
            with Image.open(image_path) as img:
                orig_width, orig_height = img.size

            messages = [
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "image",
                            "image": image_path,
                        },
                        {"type": "text", "text": self.detection_prompt},
                    ],
                }
            ]
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            image_inputs, video_inputs = process_vision_info(messages)
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            inputs = inputs.to(self.model.device)
            with torch.no_grad():
                generated_ids = self.model.generate(**inputs, max_new_tokens=512)
                generated_ids_trimmed = [
                    out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
                ]
                output_text = self.processor.batch_decode(
                    generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
                )

            # === 打印模型原始输出 ===
            logger.info(f"模型原始输出（{os.path.basename(image_path)}）：{output_text[0]}")
            # ======================

            # 解析检测结果并进行坐标修正
            results = self._parse_detection_output_with_correction(output_text[0], orig_width, orig_height, image_path)
            return results
        except Exception as e:
            logger.error(f"检测单帧图像失败: {e}")
            return []

    def _parse_detection_output_with_correction(self, output_text: str, orig_width: int, orig_height: int, image_path: str) -> List[DetectionResult]:
        """
        解析检测输出并进行坐标修正
        """
        results = []
        try:
            output_text = output_text.strip()
            # 尝试提取所有JSON对象
            json_objs = re.findall(r'\{[^\{\}]*\}', output_text)
            for obj_str in json_objs:
                try:
                    det = json.loads(obj_str)
                    model_bbox = det.get('bbox') or det.get('bbox_2d')
                    if model_bbox is not None:
                        label = det.get('label', 'unknown')
                        label = self.class_map_rev.get(label, label)

                        # 🔧 关键修正：将模型输出坐标转换为原始图像坐标
                        try:
                            corrected_bbox = convert_bbox_to_original_coords(
                                model_bbox, orig_height, orig_width
                            )

                            # 记录坐标转换信息
                            logger.debug(f"坐标转换 - 原始图像尺寸: {orig_width}x{orig_height}")
                            logger.debug(f"模型输出坐标: {model_bbox}")
                            logger.debug(f"修正后坐标: {corrected_bbox}")

                            result = DetectionResult(
                                bbox=corrected_bbox,
                                label=label,
                                confidence=det.get('confidence', 0.5),
                                frame_id='0',
                                sequence_id='',
                                temporal_score=0.5,
                                original_bbox=model_bbox,  # 保存原始模型输出
                                image_size=(orig_width, orig_height)
                            )
                            results.append(result)
                        except Exception as coord_error:
                            logger.warning(f"坐标转换失败: {coord_error}, 使用原始坐标")
                            # 如果转换失败，使用原始坐标
                            result = DetectionResult(
                                bbox=model_bbox,
                                label=label,
                                confidence=det.get('confidence', 0.5),
                                frame_id='0',
                                sequence_id='',
                                temporal_score=0.5,
                                original_bbox=model_bbox,
                                image_size=(orig_width, orig_height)
                            )
                            results.append(result)
                except Exception:
                    continue

            # 如果正则没找到，尝试原有方式
            if not results:
                json_start = output_text.find('[')
                json_end = output_text.rfind(']')
                if json_start != -1 and json_end != -1:
                    json_str = output_text[json_start:json_end+1]
                    detections = json.loads(json_str)
                    for det in detections:
                        if isinstance(det, dict):
                            model_bbox = det.get('bbox') or det.get('bbox_2d')
                            if model_bbox is not None:
                                label = det.get('label', 'unknown')
                                label = self.class_map_rev.get(label, label)

                                # 🔧 坐标修正
                                try:
                                    corrected_bbox = convert_bbox_to_original_coords(
                                        model_bbox, orig_height, orig_width
                                    )
                                    result = DetectionResult(
                                        bbox=corrected_bbox,
                                        label=label,
                                        confidence=det.get('confidence', 0.5),
                                        frame_id='0',
                                        sequence_id='',
                                        temporal_score=0.5,
                                        original_bbox=model_bbox,
                                        image_size=(orig_width, orig_height)
                                    )
                                    results.append(result)
                                except Exception:
                                    # 转换失败时使用原始坐标
                                    result = DetectionResult(
                                        bbox=model_bbox,
                                        label=label,
                                        confidence=det.get('confidence', 0.5),
                                        frame_id='0',
                                        sequence_id='',
                                        temporal_score=0.5,
                                        original_bbox=model_bbox,
                                        image_size=(orig_width, orig_height)
                                    )
                                    results.append(result)
        except Exception as e:
            logger.warning(f"解析检测输出失败: {e}")

        # 输出坐标修正统计
        if results:
            logger.info(f"图像 {os.path.basename(image_path)}: 检测到 {len(results)} 个目标，已进行坐标修正")

        return results

    def detect_sequence_with_temporal_window(self, sequence_path: str, sequence_id: str, sample_ratio: float = 0.01) -> List[DetectionResult]:
        """
        使用时间窗口检测序列中的目标 - 坐标修正版
        """
        results = []

        # 获取正确排序的图像文件
        image_files = self.get_sorted_image_files(sequence_path)

        # 计算需要处理的帧数
        total_frames = len(image_files)
        frames_to_process = max(self.frame_group_size, int(total_frames * sample_ratio))
        selected_frames = image_files[:frames_to_process]

        logger.info(f"开始检测序列 {sequence_id}，共 {total_frames} 帧，处理前 {frames_to_process} 帧 ({sample_ratio*100:.2f}%)")

        # 按时间窗口分组处理
        for start_idx in range(0, len(selected_frames), self.frame_group_size):
            end_idx = min(start_idx + self.frame_group_size, len(selected_frames))
            frame_group = selected_frames[start_idx:end_idx]

            # 获取图像路径和帧索引
            image_paths = [str(img_file) for img_file in frame_group]
            frame_indices = [start_idx + i for i in range(len(frame_group))]

            # 使用简化的帧组检测
            group_results = self.detect_frame_group(image_paths, frame_indices)

            # 设置序列ID
            for result in group_results:
                result.sequence_id = sequence_id

            results.extend(group_results)

            logger.info(f"序列 {sequence_id}: 处理窗口 {start_idx//self.frame_group_size + 1}，"
                       f"帧范围 {start_idx}-{end_idx-1}，检测到 {len(group_results)} 个目标")

            # 清理GPU内存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()

        logger.info(f"序列 {sequence_id} 检测完成，检测到 {len(results)} 个目标")
        return results

    def detect_sequence(self, sequence_path: str, sequence_id: str, sample_ratio: float = 0.01) -> List[DetectionResult]:
        """
        检测序列中的目标，只处理前sample_ratio比例的数据 - 坐标修正版
        """
        results = []
        image_files = self.get_sorted_image_files(sequence_path)

        # 计算需要处理的帧数
        total_frames = len(image_files)
        frames_to_process = max(1, int(total_frames * sample_ratio))
        selected_frames = image_files[:frames_to_process]

        logger.info(f"开始检测序列 {sequence_id}，共 {total_frames} 帧，处理前 {frames_to_process} 帧 ({sample_ratio*100:.2f}%)")

        for frame_idx, image_file in enumerate(tqdm(selected_frames, desc=f"检测序列 {sequence_id}")):
            frame_results = self.detect_single_frame(str(image_file))
            frame_id = str(extract_frame_number(image_file.name))
            for result in frame_results:
                result.frame_id = frame_id
                result.sequence_id = sequence_id
            results.extend(frame_results)
            if (frame_idx + 1) % 10 == 0:
                logger.info(f"序列 {sequence_id}: 已处理 {frame_idx + 1}/{frames_to_process} 帧，当前帧检测到 {len(frame_results)} 个目标")
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
        return results

# 从原文件导入评估器类（保持不变，因为评估逻辑不需要修改）
class ImprovedDetectionEvaluator:
    """改进的检测结果评估器 - 基于detection_evaluator.py的评估方法"""

    def __init__(self, iou_threshold: float = 0.3, consistency_iou_threshold: float = 0.3, stability_threshold: float = 0.8, class_names: List[str] = None):
        """
        初始化评估器
        """
        self.iou_threshold = iou_threshold
        self.consistency_iou_threshold = consistency_iou_threshold
        self.stability_threshold = stability_threshold
        self.class_names = class_names or ['drone', 'car', 'ship', 'bus', 'pedestrian', 'cyclist']

        # 每个视频的结果
        self.video_results = {}

    def convert_detection_to_yolo_format(self, detection: DetectionResult, img_width: int = 640, img_height: int = 640) -> Dict:
        """
        将DetectionResult转换为YOLO格式的字典
        """
        # 获取类别ID
        class_id = self.class_names.index(detection.label) if detection.label in self.class_names else 0

        # 使用检测结果中的实际图像尺寸（如果有的话）
        if detection.image_size:
            actual_width, actual_height = detection.image_size
        else:
            actual_width, actual_height = img_width, img_height

        # 转换bbox格式：从[x1, y1, x2, y2]到[x_center, y_center, width, height]（归一化）
        # 处理可能的不同bbox格式
        bbox = detection.bbox
        if len(bbox) == 4:
            x1, y1, x2, y2 = bbox
        elif len(bbox) > 4:
            # 如果bbox包含更多值，只取前4个
            x1, y1, x2, y2 = bbox[:4]
            logger.warning(f"检测结果bbox包含 {len(bbox)} 个值，只使用前4个: {bbox}")
        else:
            # 如果bbox值不足4个，跳过这个检测结果
            logger.error(f"检测结果bbox值不足4个: {bbox}")
            raise ValueError(f"Invalid bbox format: {bbox}")

        x_center = (x1 + x2) / 2 / actual_width
        y_center = (y1 + y2) / 2 / actual_height
        width = (x2 - x1) / actual_width
        height = (y2 - y1) / actual_height

        return {
            'class_id': class_id,
            'x_center': x_center,
            'y_center': y_center,
            'width': width,
            'height': height,
            'confidence': detection.confidence
        }

    def convert_groundtruth_to_yolo_format(self, gt: GroundTruth, img_width: int = 640, img_height: int = 640) -> Dict:
        """
        将GroundTruth转换为YOLO格式的字典
        """
        # 获取类别ID
        class_id = self.class_names.index(gt.label) if gt.label in self.class_names else 0

        # 使用真实标注中的实际图像尺寸（如果有的话）
        if gt.image_size:
            actual_width, actual_height = gt.image_size
        else:
            actual_width, actual_height = img_width, img_height

        # 转换bbox格式：从[x1, y1, x2, y2]到[x_center, y_center, width, height]（归一化）
        # 处理可能的不同bbox格式
        bbox = gt.bbox
        if len(bbox) == 4:
            x1, y1, x2, y2 = bbox
        elif len(bbox) > 4:
            # 如果bbox包含更多值，只取前4个
            x1, y1, x2, y2 = bbox[:4]
            logger.warning(f"真实标注bbox包含 {len(bbox)} 个值，只使用前4个: {bbox}")
        else:
            # 如果bbox值不足4个，跳过这个标注
            logger.error(f"真实标注bbox值不足4个: {bbox}")
            raise ValueError(f"Invalid bbox format: {bbox}")

        x_center = (x1 + x2) / 2 / actual_width
        y_center = (y1 + y2) / 2 / actual_height
        width = (x2 - x1) / actual_width
        height = (y2 - y1) / actual_height

        return {
            'class_id': class_id,
            'x_center': x_center,
            'y_center': y_center,
            'width': width,
            'height': height,
            'confidence': 1.0
        }

    def calculate_iou(self, box1: Dict, box2: Dict) -> float:
        """
        计算两个边界框的IoU（YOLO格式）
        """
        # 转换为左上角和右下角坐标
        x1_min = box1['x_center'] - box1['width'] / 2
        y1_min = box1['y_center'] - box1['height'] / 2
        x1_max = box1['x_center'] + box1['width'] / 2
        y1_max = box1['y_center'] + box1['height'] / 2

        x2_min = box2['x_center'] - box2['width'] / 2
        y2_min = box2['y_center'] - box2['height'] / 2
        x2_max = box2['x_center'] + box2['width'] / 2
        y2_max = box2['y_center'] + box2['height'] / 2

        # 计算交集
        inter_x_min = max(x1_min, x2_min)
        inter_y_min = max(y1_min, y2_min)
        inter_x_max = min(x1_max, x2_max)
        inter_y_max = min(y1_max, y2_max)

        if inter_x_max <= inter_x_min or inter_y_max <= inter_y_min:
            return 0.0

        inter_area = (inter_x_max - inter_x_min) * (inter_y_max - inter_y_min)

        # 计算并集
        area1 = box1['width'] * box1['height']
        area2 = box2['width'] * box2['height']
        union_area = area1 + area2 - inter_area

        return inter_area / union_area if union_area > 0 else 0.0

    def match_boxes(self, gt_boxes: List[Dict], pred_boxes: List[Dict], iou_threshold: float = None) -> Tuple:
        """
        匹配真实框和预测框
        """
        if iou_threshold is None:
            iou_threshold = self.iou_threshold

        matched_gt = set()
        matched_pred = set()
        matches = []

        # 按置信度排序预测框
        pred_boxes_sorted = sorted(enumerate(pred_boxes),
                                 key=lambda x: x[1]['confidence'], reverse=True)

        for pred_idx, pred_box in pred_boxes_sorted:
            best_iou = 0
            best_gt_idx = -1

            for gt_idx, gt_box in enumerate(gt_boxes):
                if gt_idx in matched_gt:
                    continue

                # 只匹配相同类别的框
                if gt_box['class_id'] != pred_box['class_id']:
                    continue

                iou = self.calculate_iou(gt_box, pred_box)
                if iou > best_iou:
                    best_iou = iou
                    best_gt_idx = gt_idx

            if best_iou >= iou_threshold and best_gt_idx not in matched_gt:
                matches.append((best_gt_idx, pred_idx, best_iou))
                matched_gt.add(best_gt_idx)
                matched_pred.add(pred_idx)

        return matches, matched_gt, matched_pred

    def check_frame_consistency(self, gt_boxes: List[Dict], pred_boxes: List[Dict]) -> bool:
        """
        检查单帧的时序一致性
        规则：
        - 当真值目标 <= 5个时，要求所有目标检出，且类别正确，且IoU > consistency_iou_threshold
        - 当真值目标 > 5个时，要求80%的目标检出，且类别正确，且IoU > consistency_iou_threshold
        """
        if len(gt_boxes) == 0:
            # 如果该帧没有真实目标，则认为是一致的（空帧）
            return True

        if len(pred_boxes) == 0:
            # 如果有真实目标但没有预测，则不一致
            return False

        # 使用时序一致性专用的匹配函数
        matches, matched_gt, matched_pred = self.match_boxes(gt_boxes, pred_boxes, self.consistency_iou_threshold)

        # 计算检出的目标数量
        detected_targets = len(matched_gt)
        total_targets = len(gt_boxes)

        # 根据目标数量应用不同的一致性标准
        if total_targets <= 5:
            # 目标数量 <= 5：要求所有目标都被检出
            required_detections = total_targets
            is_consistent = detected_targets >= required_detections
        else:
            # 目标数量 > 5：要求80%的目标被检出
            required_detections = int(total_targets * 0.8)
            is_consistent = detected_targets >= required_detections

        return is_consistent

    def evaluate_frame_pair(self, gt_detections: List[GroundTruth], pred_detections: List[DetectionResult], video_stats: Dict):
        """评估一对对应的真实标注和预测结果"""
        # 转换为YOLO格式，跳过格式错误的检测结果
        gt_boxes = []
        for gt in gt_detections:
            try:
                gt_box = self.convert_groundtruth_to_yolo_format(gt)
                gt_boxes.append(gt_box)
            except (ValueError, IndexError) as e:
                logger.warning(f"跳过格式错误的真实标注: {e}")
                continue

        pred_boxes = []
        for pred in pred_detections:
            try:
                pred_box = self.convert_detection_to_yolo_format(pred)
                pred_boxes.append(pred_box)
            except (ValueError, IndexError) as e:
                logger.warning(f"跳过格式错误的检测结果: {e}")
                continue

        # 检查时序一致性
        is_consistent = self.check_frame_consistency(gt_boxes, pred_boxes)
        video_stats['consistency_frames'] += 1 if is_consistent else 0
        video_stats['total_frames'] += 1

        # 记录详细的一致性统计信息（用于调试和分析）
        gt_count = len(gt_boxes)
        if gt_count > 0:
            if gt_count <= 5:
                video_stats['frames_with_targets_le5'] += 1
                if is_consistent:
                    video_stats['consistent_frames_le5'] += 1
            else:
                video_stats['frames_with_targets_gt5'] += 1
                if is_consistent:
                    video_stats['consistent_frames_gt5'] += 1

        # 按类别分组
        gt_by_class = defaultdict(list)
        pred_by_class = defaultdict(list)

        for box in gt_boxes:
            gt_by_class[box['class_id']].append(box)
            video_stats['total_gt'][box['class_id']] += 1

        for box in pred_boxes:
            pred_by_class[box['class_id']].append(box)
            video_stats['total_pred'][box['class_id']] += 1

        # 获取所有出现的类别
        all_classes = set(gt_by_class.keys()) | set(pred_by_class.keys())

        for class_id in all_classes:
            gt_class_boxes = gt_by_class[class_id]
            pred_class_boxes = pred_by_class[class_id]

            if len(gt_class_boxes) == 0 and len(pred_class_boxes) == 0:
                continue

            # 匹配该类别的框（使用标准IoU阈值）
            matches, matched_gt, matched_pred = self.match_boxes(gt_class_boxes, pred_class_boxes)

            # 统计TP, FP, FN
            tp_count = len(matches)
            fp_count = len(pred_class_boxes) - len(matched_pred)
            fn_count = len(gt_class_boxes) - len(matched_gt)

            video_stats['tp'][class_id] += tp_count
            video_stats['fp'][class_id] += fp_count
            video_stats['fn'][class_id] += fn_count

    def calculate_metrics_for_stats(self, stats: Dict, use_macro_average: bool = True) -> Dict:
        """
        根据统计数据计算指标
        use_macro_average: 是否使用宏平均计算总体指标
        """
        results = {}
        all_classes = set(stats['tp'].keys()) | set(stats['fp'].keys()) | set(stats['fn'].keys())

        # 用于宏平均的指标列表
        class_precisions = []
        class_recalls = []
        class_f1s = []
        class_false_alarm_rates = []

        # 用于微平均的总计数
        total_tp = 0
        total_fp = 0
        total_fn = 0

        # 记录参与宏平均计算的类别（数据集中存在的类别）
        valid_classes = []

        for class_id in sorted(all_classes):
            tp = stats['tp'][class_id]
            fp = stats['fp'][class_id]
            fn = stats['fn'][class_id]

            # 计算召回率 (Recall)
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0

            # 计算精确率 (Precision)
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0

            # 计算虚警率 (False Alarm Rate = 1 - Precision)
            false_alarm_rate = 1.0 - precision

            # 计算F1分数
            f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0

            class_name = self.class_names[class_id] if class_id < len(self.class_names) else f"Class_{class_id}"

            results[class_id] = {
                'class_name': class_name,
                'tp': tp,
                'fp': fp,
                'fn': fn,
                'recall': recall,
                'precision': precision,
                'false_alarm_rate': false_alarm_rate,
                'f1': f1,
                'total_gt': stats['total_gt'][class_id],
                'total_pred': stats['total_pred'][class_id]
            }

            # 判断是否为数据集中存在的类别：TP + FN > 0（有真实目标的类别）
            if tp + fn > 0:  # 数据集中存在的类别
                class_precisions.append(precision)
                class_recalls.append(recall)
                class_f1s.append(f1)
                class_false_alarm_rates.append(false_alarm_rate)
                valid_classes.append(class_id)

            # 累计用于微平均
            total_tp += tp
            total_fp += fp
            total_fn += fn

        # 计算总体指标
        if use_macro_average and len(class_precisions) > 0:
            # 宏平均：先计算各有效类别指标，再取平均
            overall_recall = sum(class_recalls) / len(class_recalls)
            overall_precision = sum(class_precisions) / len(class_precisions)
            overall_false_alarm_rate = sum(class_false_alarm_rates) / len(class_false_alarm_rates)
            overall_f1 = sum(class_f1s) / len(class_f1s)
            averaging_method = "macro"
            valid_classes_count = len(valid_classes)
        else:
            # 微平均：先累加所有TP/FP/FN，再计算指标
            overall_recall = total_tp / (total_tp + total_fn) if (total_tp + total_fn) > 0 else 0.0
            overall_precision = total_tp / (total_tp + total_fp) if (total_tp + total_fp) > 0 else 0.0
            overall_false_alarm_rate = 1.0 - overall_precision
            overall_f1 = 2 * (overall_precision * overall_recall) / (overall_precision + overall_recall) if (overall_precision + overall_recall) > 0 else 0.0
            averaging_method = "micro"
            valid_classes_count = len(all_classes)

        # 计算时序一致性
        temporal_consistency = stats['consistency_frames'] / stats['total_frames'] if stats['total_frames'] > 0 else 0.0

        results['overall'] = {
            'tp': total_tp,
            'fp': total_fp,
            'fn': total_fn,
            'recall': overall_recall,
            'precision': overall_precision,
            'false_alarm_rate': overall_false_alarm_rate,
            'f1': overall_f1,
            'total_gt': sum(stats['total_gt'].values()),
            'total_pred': sum(stats['total_pred'].values()),
            'averaging_method': averaging_method,
            'valid_classes_count': valid_classes_count,
            'valid_classes': valid_classes if use_macro_average else list(all_classes),
            'temporal_consistency': temporal_consistency,
            'consistent_frames': stats['consistency_frames'],
            'total_frames': stats['total_frames'],
            # 添加详细的一致性统计
            'frames_with_targets_le5': stats.get('frames_with_targets_le5', 0),
            'consistent_frames_le5': stats.get('consistent_frames_le5', 0),
            'frames_with_targets_gt5': stats.get('frames_with_targets_gt5', 0),
            'consistent_frames_gt5': stats.get('consistent_frames_gt5', 0)
        }

        return results

    def evaluate_sequence(self, predictions: List[DetectionResult], ground_truth: List[GroundTruth]) -> Dict:
        """
        评估单个序列的检测结果
        """
        # 按帧分组
        pred_by_frame = defaultdict(list)
        gt_by_frame = defaultdict(list)

        for pred in predictions:
            pred_by_frame[pred.frame_id].append(pred)

        for gt in ground_truth:
            gt_by_frame[gt.frame_id].append(gt)

        # 初始化该序列的统计数据
        video_stats = {
            'tp': defaultdict(int),
            'fp': defaultdict(int),
            'fn': defaultdict(int),
            'total_gt': defaultdict(int),
            'total_pred': defaultdict(int),
            'consistency_frames': 0,  # 一致的帧数
            'total_frames': 0,        # 总帧数
            # 详细的一致性统计
            'frames_with_targets_le5': 0,  # 目标数<=5的帧数
            'consistent_frames_le5': 0,    # 目标数<=5且一致的帧数
            'frames_with_targets_gt5': 0,  # 目标数>5的帧数
            'consistent_frames_gt5': 0     # 目标数>5且一致的帧数
        }

        all_frames = set(pred_by_frame.keys()) | set(gt_by_frame.keys())

        for frame_id in all_frames:
            frame_preds = pred_by_frame[frame_id]
            frame_gts = gt_by_frame[frame_id]

            # 评估当前帧
            self.evaluate_frame_pair(frame_gts, frame_preds, video_stats)

        # 计算该序列的指标（使用宏平均）
        sequence_metrics = self.calculate_metrics_for_stats(video_stats, use_macro_average=True)

        return sequence_metrics

    def evaluate_all_sequences(self, all_predictions: Dict[str, List[DetectionResult]],
                              all_ground_truth: Dict[str, List[GroundTruth]]) -> Dict:
        """
        评估所有序列的检测结果
        """
        sequence_results = {}

        # 调试信息：显示将要评估的序列
        logger.info(f"🔍 开始评估 {len(all_predictions)} 个序列")
        logger.info(f"📋 预测数据包含的序列: {list(all_predictions.keys())}")
        logger.info(f"📊 标注数据包含的序列: {list(all_ground_truth.keys())}")

        for seq_id in tqdm(all_predictions.keys(), desc="评估序列", unit="seq"):
            if seq_id in all_ground_truth:
                seq_result = self.evaluate_sequence(
                    all_predictions[seq_id],
                    all_ground_truth[seq_id]
                )
                sequence_results[seq_id] = seq_result

                # 保存该序列的结果
                self.video_results[seq_id] = {
                    'frames_processed': seq_result['overall']['total_frames'],
                    'frames_gt': seq_result['overall']['total_frames'],
                    'frames_pred': seq_result['overall']['total_frames'],
                    'metrics': seq_result
                }

                # 输出当前序列的评估结果
                logger.info(f"序列 {seq_id} 评估完成: 精确率={seq_result['overall']['precision']:.4f}, "
                           f"召回率={seq_result['overall']['recall']:.4f}, "
                           f"时序一致性={seq_result['overall']['temporal_consistency']:.4f}")

        # 计算总体指标（直接基于视频总体指标进行平均）
        overall_results = self.calculate_overall_metrics_direct_video_average()

        return {
            'sequence_results': sequence_results,
            'overall_results': overall_results
        }

    def calculate_overall_metrics_direct_video_average(self) -> Dict:
        """直接基于视频总体指标进行平均"""
        if not self.video_results:
            return {}

        # 直接收集各视频的总体指标（不分类别）
        video_overall_precisions = []
        video_overall_recalls = []
        video_overall_f1s = []
        video_overall_false_alarm_rates = []
        video_temporal_consistencies = []

        # 累计统计用于显示
        total_stats = {
            'tp': 0,
            'fp': 0,
            'fn': 0,
            'total_gt': 0,
            'total_pred': 0,
            'total_frames': 0,
            'total_consistent_frames': 0,
            'total_frames_with_targets_le5': 0,
            'total_consistent_frames_le5': 0,
            'total_frames_with_targets_gt5': 0,
            'total_consistent_frames_gt5': 0
        }

        # 统计时空稳定性
        stable_videos = 0  # 时序一致性超过阈值的视频数

        for video_name, video_result in self.video_results.items():
            metrics = video_result['metrics']

            # 直接收集视频总体指标
            if 'overall' in metrics:
                overall_metric = metrics['overall']
                video_overall_precisions.append(overall_metric['precision'])
                video_overall_recalls.append(overall_metric['recall'])
                video_overall_f1s.append(overall_metric['f1'])
                video_overall_false_alarm_rates.append(overall_metric['false_alarm_rate'])
                video_temporal_consistencies.append(overall_metric['temporal_consistency'])

                # 累计统计
                total_stats['tp'] += overall_metric['tp']
                total_stats['fp'] += overall_metric['fp']
                total_stats['fn'] += overall_metric['fn']
                total_stats['total_gt'] += overall_metric['total_gt']
                total_stats['total_pred'] += overall_metric['total_pred']
                total_stats['total_frames'] += overall_metric['total_frames']
                total_stats['total_consistent_frames'] += overall_metric['consistent_frames']
                total_stats['total_frames_with_targets_le5'] += overall_metric.get('frames_with_targets_le5', 0)
                total_stats['total_consistent_frames_le5'] += overall_metric.get('consistent_frames_le5', 0)
                total_stats['total_frames_with_targets_gt5'] += overall_metric.get('frames_with_targets_gt5', 0)
                total_stats['total_consistent_frames_gt5'] += overall_metric.get('consistent_frames_gt5', 0)

                # 检查是否为稳定视频
                if overall_metric['temporal_consistency'] >= self.stability_threshold:
                    stable_videos += 1

        # 直接计算总体的视频级平均指标
        overall_precision = sum(video_overall_precisions) / len(video_overall_precisions) if video_overall_precisions else 0.0
        overall_recall = sum(video_overall_recalls) / len(video_overall_recalls) if video_overall_recalls else 0.0
        overall_f1 = sum(video_overall_f1s) / len(video_overall_f1s) if video_overall_f1s else 0.0
        overall_false_alarm_rate = sum(video_overall_false_alarm_rates) / len(video_overall_false_alarm_rates) if video_overall_false_alarm_rates else 0.0
        overall_temporal_consistency = sum(video_temporal_consistencies) / len(video_temporal_consistencies) if video_temporal_consistencies else 0.0

        # 计算时空序列稳定性
        spatiotemporal_stability = stable_videos / len(self.video_results) if len(self.video_results) > 0 else 0.0

        results = {
            'overall': {
                'tp': total_stats['tp'],
                'fp': total_stats['fp'],
                'fn': total_stats['fn'],
                'recall': overall_recall,
                'precision': overall_precision,
                'false_alarm_rate': overall_false_alarm_rate,
                'f1': overall_f1,
                'total_gt': total_stats['total_gt'],
                'total_pred': total_stats['total_pred'],
                'averaging_method': 'direct_video_level',
                'video_count': len(self.video_results),
                'temporal_consistency': overall_temporal_consistency,
                'total_frames': total_stats['total_frames'],
                'total_consistent_frames': total_stats['total_consistent_frames'],
                'spatiotemporal_stability': spatiotemporal_stability,
                'stable_videos': stable_videos,
                'stability_threshold': self.stability_threshold,
                'consistency_iou_threshold': self.consistency_iou_threshold,
                # 添加详细的一致性统计
                'total_frames_with_targets_le5': total_stats['total_frames_with_targets_le5'],
                'total_consistent_frames_le5': total_stats['total_consistent_frames_le5'],
                'total_frames_with_targets_gt5': total_stats['total_frames_with_targets_gt5'],
                'total_consistent_frames_gt5': total_stats['total_consistent_frames_gt5']
            }
        }

        return results

    def print_overall_results(self, overall_metrics: Dict):
        """打印总体结果 - 显示每个视频的性能指标"""
        print("\n" + "="*88)
        print("总体评估结果 (各视频性能指标) - 坐标修正版")
        print("="*88)

        if not overall_metrics or not self.video_results:
            print("没有结果可显示")
            return

        # 显示各视频的性能指标
        print(f"{'视频名称':<20} {'召回率':<8} {'精确率':<8} {'虚警率':<8} {'时序一致性':<10} {'TP':<6} {'FP':<6} {'FN':<6} {'帧数':<6}")
        print("-" * 88)

        # 按视频名称排序显示各视频结果
        for video_name in sorted(self.video_results.keys()):
            video_result = self.video_results[video_name]
            metrics = video_result['metrics']

            if 'overall' in metrics:
                r = metrics['overall']
                frames = video_result['frames_processed']
                temporal_consistency = r['temporal_consistency']
                stability_mark = "✓" if temporal_consistency >= self.stability_threshold else " "

                print(f"{video_name:<20} {r['recall']:<8.3f} {r['precision']:<8.3f} {r['false_alarm_rate']:<8.3f} "
                      f"{temporal_consistency:<9.3f}{stability_mark} {r['tp']:<6} {r['fp']:<6} {r['fn']:<6} {frames:<6}")

        # 显示总体平均结果
        if 'overall' in overall_metrics:
            print("-" * 88)
            r = overall_metrics['overall']
            video_count = r.get('video_count', 0)
            total_frames = sum(video_result['frames_processed'] for video_result in self.video_results.values())

            print(f"{'总体平均':<20} {r['recall']:<8.3f} {r['precision']:<8.3f} {r['false_alarm_rate']:<8.3f} "
                  f"{r['temporal_consistency']:<10.3f} {r['tp']:<6} {r['fp']:<6} {r['fn']:<6} {total_frames:<6}")

            print(f"\n详细统计:")
            print(f"🔧 坐标修正版本 - 已自动修正Qwen2.5-VL模型的坐标映射问题")
            print(f"目标识别精度 - 平均召回率: {r['recall']:.4f}")
            print(f"目标识别精度 - 平均精确率: {r['precision']:.4f}")
            print(f"目标识别精度 - F1分数: {r['f1']:.4f}")
            print(f"时空序列稳定性: {r['spatiotemporal_stability']:.4f} ({r['stable_videos']}/{video_count} 个视频超过{r['stability_threshold']:.0%}阈值)")
            print(f"总真实目标数: {r['total_gt']}")
            print(f"总预测目标数: {r['total_pred']}")
            print(f"总处理帧数: {total_frames}")
            print(f"IoU阈值: {self.iou_threshold}")
            print(f"评估视频数: {video_count}")
            print(f"时序一致性IoU阈值: {r['consistency_iou_threshold']}")

            # 计算总体的≤5目标和>5目标一致性率
            total_frames_le5 = r.get('total_frames_with_targets_le5', 0)
            total_consistent_le5 = r.get('total_consistent_frames_le5', 0)
            total_frames_gt5 = r.get('total_frames_with_targets_gt5', 0)
            total_consistent_gt5 = r.get('total_consistent_frames_gt5', 0)

            overall_le5_rate = total_consistent_le5 / total_frames_le5 if total_frames_le5 > 0 else 0.0
            overall_gt5_rate = total_consistent_gt5 / total_frames_gt5 if total_frames_gt5 > 0 else 0.0

            print(f"")
            print(f"时序一致性统计:")
            print(f"  目标数≤5帧: {total_consistent_le5}/{total_frames_le5} ({overall_le5_rate:.3f}) - 要求100%检出")
            print(f"  目标数>5帧: {total_consistent_gt5}/{total_frames_gt5} ({overall_gt5_rate:.3f}) - 要求80%检出")
            print(f"")
            print(f"时序一致性规则:")
            print(f"  - 目标数 ≤ 5: 要求所有目标检出，且类别正确，且IoU > {r['consistency_iou_threshold']}")
            print(f"  - 目标数 > 5: 要求80%目标检出，且类别正确，且IoU > {r['consistency_iou_threshold']}")
            print(f"总体指标计算方式: 直接视频级平均")
            print(f"🔧 坐标修正: 已自动处理Qwen2.5-VL的图像尺寸调整问题")

# 从原文件导入其余的评估方法和数据加载函数
def load_class_map(class_json_path: str):
    with open(class_json_path, 'r', encoding='utf-8') as f:
        class_map = json.load(f)
    # 反向映射：所有英文名映射成自己
    class_map_rev = {v: v for v in class_map.values()}
    # 可扩展：如有中英文对照，可在此补充
    zh2en = {"无人机": "drone", "汽车": "car", "轮船": "ship", "公交车": "bus", "行人": "pedestrian", "骑行者": "cyclist", "弱小目标": "unknown"}
    class_map_rev.update(zh2en)
    return class_map, class_map_rev

def load_yolo_ground_truth_with_bbox_correction(labels_root: str, class_json_path: str, img_root: str, sample_ratio: float = 0.01, target_sequences: list = None):
    """
    加载YOLO格式的真实标注数据，并进行坐标修正
    """
    class_map, _ = load_class_map(class_json_path)
    ground_truth = defaultdict(list)

    for seq in sorted(os.listdir(labels_root)):
        seq_path = os.path.join(labels_root, seq)
        if not os.path.isdir(seq_path):
            continue

        # 如果指定了目标序列，只处理这些序列
        if target_sequences and seq not in target_sequences:
            continue

        # 获取所有标注文件并使用自然排序
        label_files = []
        for label_file in os.listdir(seq_path):
            if label_file.endswith('.txt'):
                label_files.append(label_file)

        # 使用自然排序确保正确的数字顺序
        label_files.sort(key=lambda x: natural_sort_key(x))

        # 只处理前sample_ratio比例的标注文件
        total_labels = len(label_files)
        labels_to_process = max(1, int(total_labels * sample_ratio))
        selected_labels = label_files[:labels_to_process]

        logger.info(f"序列 {seq} 标注: 共 {total_labels} 个文件，处理前 {labels_to_process} 个 ({sample_ratio*100:.2f}%)")

        # 显示文件命名格式（用于调试）
        if len(label_files) > 0:
            logger.info(f"序列 {seq} 标注文件格式示例: {label_files[0]}")

        # 获取对应的图像文件列表（按相同顺序排序）
        img_seq_path = os.path.join(img_root, seq)
        if not os.path.exists(img_seq_path):
            logger.warning(f"图像目录不存在: {img_seq_path}")
            continue

        image_files = []
        for ext in ['.bmp', '.jpg', '.jpeg', '.png']:
            image_files.extend(Path(img_seq_path).glob(f'*{ext}'))

        # 使用相同的自然排序确保顺序一致
        image_files.sort(key=lambda x: natural_sort_key(x.name))

        # 显示图像文件格式（用于调试）
        if len(image_files) > 0:
            logger.info(f"序列 {seq} 图像文件格式示例: {image_files[0].name}")

        # 确保图像文件数量足够
        if len(image_files) < len(selected_labels):
            logger.warning(f"序列 {seq}: 图像文件数量({len(image_files)}) < 标注文件数量({len(selected_labels)})")
            # 调整处理数量为较小值
            labels_to_process = min(len(selected_labels), len(image_files))
            selected_labels = selected_labels[:labels_to_process]

        logger.info(f"序列 {seq}: 将通过索引对应处理 {len(selected_labels)} 个文件对")

        for idx, label_file in enumerate(selected_labels):
            label_path = os.path.join(seq_path, label_file)
            # 修复：使用连续的索引作为frame_id，确保与检测结果匹配
            frame_id = str(idx)

            # 通过索引对应获取图像文件，而不是通过文件名匹配
            if idx < len(image_files):
                img_file = str(image_files[idx])
            else:
                logger.warning(f"序列 {seq}: 标注文件 {label_file} 没有对应的图像文件")
                continue

            # 获取图像尺寸
            with Image.open(img_file) as img:
                w, h = img.size

            # 解析标注文件
            has_annotations = False
            with open(label_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if not line:
                        continue
                    parts = line.split()
                    if len(parts) < 5:
                        continue
                    cls_id, cx, cy, bw, bh = parts[:5]
                    label = class_map.get(str(cls_id), 'unknown')
                    cx, cy, bw, bh = map(float, [cx, cy, bw, bh])

                    # 转换为绝对坐标
                    x1 = (cx - bw/2) * w
                    y1 = (cy - bh/2) * h
                    x2 = (cx + bw/2) * w
                    y2 = (cy + bh/2) * h

                    # 🔧 关键修正：将标注坐标转换为模型内部坐标系
                    # 这样评估时就能正确匹配
                    try:
                        model_coords = convert_bbox_to_model_coords([x1, y1, x2, y2], h, w)
                        logger.debug(f"标注坐标转换 - 原始: [{x1:.1f}, {y1:.1f}, {x2:.1f}, {y2:.1f}] -> 模型坐标: {model_coords}")

                        gt = GroundTruth(
                            bbox=model_coords,  # 使用转换后的坐标
                            label=label,
                            frame_id=frame_id,
                            sequence_id=seq,
                            image_size=(w, h)
                        )
                        ground_truth[seq].append(gt)
                        has_annotations = True
                    except Exception as e:
                        logger.warning(f"标注坐标转换失败: {e}, 使用原始坐标")
                        # 转换失败时使用原始坐标
                        gt = GroundTruth(
                            bbox=[x1, y1, x2, y2],
                            label=label,
                            frame_id=frame_id,
                            sequence_id=seq,
                            image_size=(w, h)
                        )
                        ground_truth[seq].append(gt)
                        has_annotations = True

            # 确保即使是空标注文件也在ground_truth中有对应的序列条目
            if not has_annotations:
                if seq not in ground_truth:
                    ground_truth[seq] = []

    return ground_truth

def save_results_with_correction_info(results: List[DetectionResult], output_path: str):
    """
    保存检测结果，包含坐标修正信息
    """
    output_dir = os.path.dirname(output_path)
    if output_dir and not os.path.exists(output_dir):
        os.makedirs(output_dir)

    output_data = []
    for result in results:
        data = {
            'sequence_id': result.sequence_id,
            'frame_id': result.frame_id,
            'bbox': result.bbox,  # 修正后的坐标
            'label': result.label,
            'confidence': result.confidence,
            'temporal_score': result.temporal_score,
            'original_bbox': result.original_bbox,  # 模型原始输出坐标
            'image_size': result.image_size,  # 图像尺寸
            'coordinate_corrected': result.original_bbox != result.bbox  # 是否进行了坐标修正
        }
        output_data.append(data)

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(output_data, f, ensure_ascii=False, indent=2)

    logger.info(f"检测结果已保存到: {output_path}")

    # 统计坐标修正信息
    corrected_count = sum(1 for data in output_data if data['coordinate_corrected'])
    total_count = len(output_data)
    logger.info(f"坐标修正统计: {corrected_count}/{total_count} 个检测结果进行了坐标修正")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='红外弱小目标检测系统 - 坐标修正版')
    parser.add_argument('--base_model_path', type=str, required=True, help='基础Qwen2.5-VL模型路径')
    parser.add_argument('--lora_model_path', type=str, help='LoRA微调模型路径（可选）')
    parser.add_argument('--data_path', type=str, required=True, help='数据集路径')
    parser.add_argument('--output_path', type=str, default='Output/finetuned_detection_results_bbox_corrected.json', help='输出结果路径')
    parser.add_argument('--annotation_path', type=str, help='真实标注文件路径（用于评估，可为labels目录）')
    parser.add_argument('--class_json', type=str, default='class.json', help='类别映射json路径')
    parser.add_argument('--device', type=str, default='auto', help='设备类型')
    parser.add_argument('--iou_threshold', type=float, default=0.3, help='IoU阈值')
    parser.add_argument('--consistency_iou_threshold', type=float, default=0.3, help='时序一致性IoU阈值')
    parser.add_argument('--stability_threshold', type=float, default=0.8, help='时空稳定性阈值')
    parser.add_argument('--sample_ratio', type=float, default=0.01, help='每个序列的采样比例，默认0.01表示前1%')
    parser.add_argument('--frame_group_size', type=int, default=5, help='时间窗口大小（帧数），建议5-8帧')
    parser.add_argument('--sequences', type=str, nargs='*', help='指定要检测的序列名称，不指定则检测所有序列')

    args = parser.parse_args()

    print("🔧 红外弱小目标检测系统 - 坐标修正版")
    print("="*60)
    print("✨ 新功能: 自动修正Qwen2.5-VL模型的坐标映射问题")
    print("📋 解决问题: 模型输出坐标与原始图像坐标不匹配")
    print("🎯 适用场景: 非28倍数尺寸图像（如640×512、256×256）的精确目标定位")
    print("="*60)

    # 初始化时序检测器
    detector = InfraredTargetDetectorCorrected(
        args.base_model_path,
        args.lora_model_path,
        args.device,
        args.class_json,
        args.frame_group_size
    )

    # 获取所有序列
    data_path = Path(args.data_path)
    sequences = [d for d in data_path.iterdir() if d.is_dir()]
    sequences.sort()

    # 根据命令行参数选择要检测的序列
    if args.sequences:
        original_count = len(sequences)
        sequences = [seq for seq in sequences if seq.name in args.sequences]
        logger.info(f"🔍 从 {original_count} 个序列中筛选出 {len(sequences)} 个指定序列")
        logger.info(f"📋 指定检测的序列: {args.sequences}")
        logger.info(f"✅ 实际找到的序列: {[seq.name for seq in sequences]}")

        # 检查是否有缺失的序列
        found_names = [seq.name for seq in sequences]
        missing = [name for name in args.sequences if name not in found_names]
        if missing:
            logger.warning(f"⚠️ 以下指定序列未找到: {missing}")
    else:
        logger.info(f"检测所有 {len(sequences)} 个序列: {[seq.name for seq in sequences]}")

    # 检测序列
    all_results = []
    print(f"\n🚀 开始检测 {len(sequences)} 个序列，每个序列处理前 {args.sample_ratio*100:.2f}% 的数据...")
    print(f"🔧 使用坐标修正功能，确保检测框准确对应原始图像位置")
    print(f"⏱️ 使用 {args.frame_group_size} 帧时间窗口进行多帧检测")
    total_detections = 0

    for seq_idx, seq_dir in enumerate(tqdm(sequences, desc="检测序列", unit="seq")):
        seq_id = seq_dir.name
        logger.info(f"开始检测序列: {seq_id} ({seq_idx+1}/{len(sequences)})")

        # 使用时间窗口检测
        seq_results = detector.detect_sequence_with_temporal_window(str(seq_dir), seq_id, args.sample_ratio)

        if len(seq_results) > 0:
            logger.info(f"序列 {seq_id} 检测结果前2个: {[(r.frame_id, r.bbox, r.temporal_score) for r in seq_results[:2]]}")

        all_results.extend(seq_results)
        total_detections += len(seq_results)
        logger.info(f"序列 {seq_id} 检测完成，检测到 {len(seq_results)} 个目标")

        # 如果没有检测到目标，记录警告
        if len(seq_results) == 0:
            logger.warning(f"⚠️ 序列 {seq_id} 没有检测到任何目标，这可能影响评估结果")

    logger.info(f"所有序列检测完成，总共检测到 {total_detections} 个目标")

    # 保存检测结果（包含坐标修正信息）
    save_results_with_correction_info(all_results, args.output_path)

    print(f"\n🎉 检测完成!")
    print(f"📁 结果已保存到: {args.output_path}")
    print(f"📊 总检测数: {total_detections}")
    print(f"🔧 所有检测结果已进行坐标修正，确保与原始图像坐标对应")

    # 评估检测结果
    if args.annotation_path:
        logger.info("开始评估检测结果")

        # 加载类别名称
        class_map, _ = load_class_map(args.class_json)
        class_names = list(class_map.values())

        # 初始化改进的评估器
        evaluator = ImprovedDetectionEvaluator(
            iou_threshold=args.iou_threshold,
            consistency_iou_threshold=args.consistency_iou_threshold,
            stability_threshold=args.stability_threshold,
            class_names=class_names
        )

        if os.path.isdir(args.annotation_path):
            # 加载所有指定序列的标注（使用坐标修正版本）
            target_sequences = [seq.name for seq in sequences]  # 使用所有指定的序列
            ground_truth = load_yolo_ground_truth_with_bbox_correction(
                args.annotation_path, args.class_json, args.data_path,
                args.sample_ratio, target_sequences
            )
        else:
            logger.error("标注路径必须是目录")
            return

        predictions_by_seq = defaultdict(list)
        for result in all_results:
            predictions_by_seq[result.sequence_id].append(result)

        # 确保所有指定的序列都在predictions_by_seq中，即使没有检测结果
        for seq in sequences:
            if seq.name not in predictions_by_seq:
                predictions_by_seq[seq.name] = []  # 空列表表示没有检测到目标

        # 显示评估前的统计信息
        print(f"\n" + "="*80)
        print(f"🔍 评估前统计信息 (坐标修正版)")
        print(f"="*80)
        print(f"📋 指定的序列: {[seq.name for seq in sequences]}")
        print(f"✅ 有检测结果的序列: {[seq_id for seq_id, results in predictions_by_seq.items() if len(results) > 0]}")
        print(f"❌ 没有检测结果的序列: {[seq_id for seq_id, results in predictions_by_seq.items() if len(results) == 0]}")
        print(f"📊 有标注数据的序列: {list(ground_truth.keys())}")
        print(f"🎯 将要评估的序列: {list(predictions_by_seq.keys())}")
        print(f"📈 评估序列总数: {len(predictions_by_seq)}")
        print(f"🔧 坐标修正: 检测结果和标注数据都已进行坐标修正，确保在同一坐标系下比较")
        print(f"="*80)

        print(f"\n开始评估 {len(predictions_by_seq)} 个序列的检测结果...")
        evaluation_results = evaluator.evaluate_all_sequences(predictions_by_seq, ground_truth)

        logger.info("所有序列评估完成")

        # 使用改进的结果显示方式
        evaluator.print_overall_results(evaluation_results['overall_results'])

        # 保存评估结果
        eval_output_path = args.output_path.replace('.json', '_evaluation_bbox_corrected.json')
        output_dir = os.path.dirname(eval_output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        with open(eval_output_path, 'w', encoding='utf-8') as f:
            json.dump(evaluation_results, f, ensure_ascii=False, indent=2)
        logger.info(f"评估结果已保存到: {eval_output_path}")

        print(f"\n🎯 坐标修正版评估完成!")
        print(f"✨ 主要改进: 解决了Qwen2.5-VL模型在非28倍数尺寸图像上的坐标偏移问题")
        print(f"📊 评估结果更加准确，真实反映模型在原始图像上的检测性能")

if __name__ == "__main__":
    main()
