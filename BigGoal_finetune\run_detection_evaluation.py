#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BigGoal弱小目标检测和评估运行脚本
先执行检测，再执行评估
"""

import os
import sys
import argparse
import subprocess
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_command(cmd, description):
    """运行命令并处理结果"""
    logger.info(f"开始{description}...")
    logger.info(f"执行命令: {' '.join(cmd)}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=True, text=True)
        logger.info(f"{description}完成")
        if result.stdout:
            logger.info(f"输出: {result.stdout}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"{description}失败: {e}")
        if e.stdout:
            logger.error(f"标准输出: {e.stdout}")
        if e.stderr:
            logger.error(f"错误输出: {e.stderr}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='BigGoal弱小目标检测和评估')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--data_path', type=str, required=True, help='图像数据路径')
    parser.add_argument('--annotation_path', type=str, required=True, help='标注数据路径')
    parser.add_argument('--class_json', type=str, required=True, help='类别映射文件')
    parser.add_argument('--output_dir', type=str, default='output', help='输出目录')
    parser.add_argument('--sample_ratio', type=float, default=0.2, help='采样比例')
    parser.add_argument('--iou_threshold', type=float, default=0.3, help='IoU阈值')
    parser.add_argument('--skip_detection', action='store_true', help='跳过检测，直接评估')
    
    args = parser.parse_args()
    
    # 确保输出目录存在
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 获取脚本目录
    script_dir = Path(__file__).parent
    detection_script = script_dir / "biggoal_detection.py"
    evaluation_script = script_dir / "biggoal_evaluation.py"
    
    # 检测结果文件路径
    detection_results_file = os.path.join(args.output_dir, "biggoal_detection_results.json")
    
    success = True
    
    # 步骤1: 执行检测（如果不跳过）
    if not args.skip_detection:
        detection_cmd = [
            sys.executable, str(detection_script),
            '--model_path', args.model_path,
            '--data_path', args.data_path,
            '--output_dir', args.output_dir,
            '--sample_ratio', str(args.sample_ratio)
        ]
        
        if not run_command(detection_cmd, "目标检测"):
            success = False
    else:
        logger.info("跳过检测步骤")
        if not os.path.exists(detection_results_file):
            logger.error(f"检测结果文件不存在: {detection_results_file}")
            success = False
    
    # 步骤2: 执行评估（如果检测成功或跳过检测且结果文件存在）
    if success:
        evaluation_cmd = [
            sys.executable, str(evaluation_script),
            '--detection_results', detection_results_file,
            '--data_path', args.data_path,
            '--annotation_path', args.annotation_path,
            '--class_json', args.class_json,
            '--output_dir', args.output_dir,
            '--sample_ratio', str(args.sample_ratio),
            '--iou_threshold', str(args.iou_threshold)
        ]
        
        if not run_command(evaluation_cmd, "结果评估"):
            success = False
    
    # 输出最终结果
    if success:
        logger.info("🎉 检测和评估流程全部完成！")
        logger.info(f"检测结果: {detection_results_file}")
        logger.info(f"评估结果: {os.path.join(args.output_dir, 'biggoal_evaluation_results.json')}")
    else:
        logger.error("❌ 流程执行失败，请检查错误信息")
        sys.exit(1)

if __name__ == "__main__":
    main()
