# BigGoal弱小目标检测和评估系统

本系统将原始的检测评估代码拆分为三个独立的模块：

## 文件说明

### 1. `biggoal_detection.py` - 检测模块
负责使用训练好的模型对图像序列进行弱小目标检测。

**主要功能：**
- 加载Qwen2.5-VL模型
- 对指定序列进行目标检测
- 使用时间窗口处理提高检测稳定性
- 输出JSON格式的检测结果

### 2. `biggoal_evaluation.py` - 评估模块
负责对检测结果进行评估分析。

**主要功能：**
- 加载检测结果和标注数据
- 计算精确率、召回率、F1分数
- 评估时序一致性
- 输出详细的评估报告

### 3. `run_detection_evaluation.py` - 运行脚本
提供一键运行检测和评估的便捷脚本。

## 使用方法

### 方法1：一键运行（推荐）

```bash
python run_detection_evaluation.py \
    --model_path /path/to/your/model \
    --data_path /path/to/image/data \
    --annotation_path /path/to/annotations \
    --class_json /path/to/class_mapping.json \
    --output_dir output \
    --sample_ratio 0.2
```

### 方法2：分步运行

#### 步骤1：执行检测
```bash
python biggoal_detection.py \
    --model_path /path/to/your/model \
    --data_path /path/to/image/data \
    --output_dir output \
    --sample_ratio 0.2
```

#### 步骤2：执行评估
```bash
python biggoal_evaluation.py \
    --detection_results output/biggoal_detection_results.json \
    --data_path /path/to/image/data \
    --annotation_path /path/to/annotations \
    --class_json /path/to/class_mapping.json \
    --output_dir output \
    --sample_ratio 0.2 \
    --iou_threshold 0.3
```

## 参数说明

### 通用参数
- `--model_path`: 训练好的模型路径
- `--data_path`: 图像数据根目录
- `--annotation_path`: 标注数据根目录
- `--class_json`: 类别映射JSON文件路径
- `--output_dir`: 输出目录（默认：output）
- `--sample_ratio`: 采样比例（默认：0.2，即处理前20%的帧）

### 评估专用参数
- `--detection_results`: 检测结果JSON文件路径
- `--iou_threshold`: IoU阈值（默认：0.3）

### 运行脚本专用参数
- `--skip_detection`: 跳过检测步骤，直接评估已有结果

## 输出文件

### 检测结果
- `biggoal_detection_results.json`: 包含所有检测到的目标信息

### 评估结果
- `biggoal_evaluation_results.json`: 包含详细的评估指标

## 目标序列

系统默认处理以下5个序列：
- data03
- data05  
- data20
- data21
- data22

## 支持的目标类别

- drone (无人机)
- car (汽车)
- ship (轮船)
- bus (公交车)
- pedestrian (行人)
- cyclist (骑行者)

## 注意事项

1. 确保模型路径正确，且模型文件完整
2. 图像数据和标注数据的目录结构要匹配
3. 类别映射文件格式要正确
4. 建议在GPU环境下运行以提高检测速度
5. 采样比例可根据实际需求调整，比例越高处理时间越长但结果越准确

## 示例命令

```bash
# 完整运行示例
python run_detection_evaluation.py \
    --model_path ./models/qwen2.5-vl-finetuned \
    --data_path ./data/images \
    --annotation_path ./data/labels \
    --class_json ./data/class_mapping.json \
    --output_dir ./results \
    --sample_ratio 0.1

# 仅评估已有检测结果
python run_detection_evaluation.py \
    --model_path ./models/qwen2.5-vl-finetuned \
    --data_path ./data/images \
    --annotation_path ./data/labels \
    --class_json ./data/class_mapping.json \
    --output_dir ./results \
    --skip_detection
```
