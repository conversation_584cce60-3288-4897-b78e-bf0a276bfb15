#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BigGoal弱小目标检测系统
针对5个序列的红外弱小目标检测
"""

import os
import re
import json
import numpy as np
import torch
from PIL import Image
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import logging
from tqdm import tqdm
import argparse
from pathlib import Path
import sys

# 添加父目录到路径
current_dir = Path(__file__).parent
parent_dir = current_dir.parent
sys.path.insert(0, str(parent_dir))

from transformers import Qwen2_5_VLForConditionalGeneration, AutoProcessor
from qwen_vl_utils import process_vision_info

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def natural_sort_key(filename: str) -> List:
    """自然排序键函数"""
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return [int(numbers[-1])]
    else:
        return [filename.lower()]

def extract_frame_number(filename: str) -> int:
    """从文件名中提取帧序号"""
    numbers = re.findall(r'\d+', filename)
    if numbers:
        return int(numbers[-1])
    else:
        return 0

@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: List[float]  # [x1, y1, x2, y2]
    label: str
    confidence: float
    frame_id: str
    sequence_id: str
    temporal_score: float = 0.5

class BigGoalDetector:
    """BigGoal弱小目标检测器"""
    
    def __init__(self, model_path: str, device: str = "cuda", target_sequences: List[str] = None):
        """
        初始化检测器

        Args:
            model_path: 模型路径
            device: 设备
            target_sequences: 目标序列列表，如果为None则使用默认值
        """
        self.device = device
        self.target_sequences = target_sequences or ["data_02", "data_05", "data_06", "data_14", "data_15"]

        logger.info(f"加载模型: {model_path}")
        self.model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
            model_path, torch_dtype="auto", device_map="auto"
        )
        self.processor = AutoProcessor.from_pretrained(model_path)

        logger.info(f"目标序列: {self.target_sequences}")

    def discover_sequences(self, data_path: str) -> List[str]:
        """自动发现数据目录中的序列"""
        if not os.path.exists(data_path):
            logger.warning(f"数据路径不存在: {data_path}")
            return []

        sequences = []
        for item in os.listdir(data_path):
            item_path = os.path.join(data_path, item)
            if os.path.isdir(item_path):
                # 检查是否包含图像文件
                image_files = []
                for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
                    image_files.extend(Path(item_path).glob(ext))
                if image_files:
                    sequences.append(item)

        sequences.sort()
        logger.info(f"发现的序列: {sequences}")
        return sequences
    
    def detect_single_frame(self, image_path: str) -> List[Dict]:
        """检测单帧图像"""
        try:
            image = Image.open(image_path).convert('RGB')
            
            # 构建检测提示
            prompt = (
                "请识别图像中的所有弱小目标，包括无人机、汽车、轮船、公交车、行人、骑行者等红外目标。\n"
                "请严格以JSON数组格式输出所有目标，且label字段必须为以下英文类别之一："
                "drone, car, ship, bus, pedestrian, cyclist。\n"
                "格式：[{\"bbox\":[x1,y1,x2,y2],\"label\":\"类别英文名\"}, ...]\n"
                "如果没有检测到目标，请返回空数组[]。"
            )
            
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": prompt}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            image_inputs, video_inputs = process_vision_info(messages)
            inputs = self.processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
            inputs = inputs.to(self.device)
            
            # 生成结果
            generated_ids = self.model.generate(**inputs, max_new_tokens=512)
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            output_text = self.processor.batch_decode(
                generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
            )[0]
            
            # 解析JSON结果
            try:
                detections = json.loads(output_text)
                if not isinstance(detections, list):
                    return []
                return detections
            except json.JSONDecodeError:
                logger.warning(f"JSON解析失败: {output_text}")
                return []
                
        except Exception as e:
            logger.error(f"检测单帧图像失败: {e}")
            return []
    
    def detect_sequence_with_temporal_window(self, data_path: str, sequence_id: str, 
                                           sample_ratio: float = 0.2, window_size: int = 5) -> List[DetectionResult]:
        """使用时间窗口检测序列"""
        sequence_path = os.path.join(data_path, sequence_id)
        if not os.path.exists(sequence_path):
            logger.warning(f"序列路径不存在: {sequence_path}")
            return []
        
        # 获取所有图像文件
        image_files = []
        for ext in ['*.jpg', '*.jpeg', '*.png', '*.bmp']:
            image_files.extend(Path(sequence_path).glob(ext))
        
        # 自然排序
        image_files = sorted(image_files, key=lambda x: natural_sort_key(x.name))
        
        # 采样
        total_frames = len(image_files)
        sample_count = max(1, int(total_frames * sample_ratio))
        selected_files = image_files[:sample_count]
        
        logger.info(f"序列 {sequence_id}: 总帧数 {total_frames}, 处理前 {sample_count} 帧 ({sample_ratio*100:.2f}%)")
        
        all_detections = []
        
        # 分窗口处理
        for i in range(0, len(selected_files), window_size):
            window_files = selected_files[i:i+window_size]
            window_detections = []
            
            for image_file in window_files:
                frame_id = str(extract_frame_number(image_file.name))
                detections = self.detect_single_frame(str(image_file))
                
                for det in detections:
                    if 'bbox' in det and 'label' in det:
                        result = DetectionResult(
                            bbox=det['bbox'],
                            label=det['label'],
                            confidence=det.get('confidence', 0.5),
                            frame_id=frame_id,
                            sequence_id=sequence_id,
                            temporal_score=0.8  # 时序得分
                        )
                        window_detections.append(result)
            
            all_detections.extend(window_detections)
            
            logger.info(f"序列 {sequence_id}: 处理窗口 {i//window_size + 1}，"
                       f"帧范围 {extract_frame_number(window_files[0].name)}-"
                       f"{extract_frame_number(window_files[-1].name)}，"
                       f"检测到 {len(window_detections)} 个目标")
        
        logger.info(f"序列 {sequence_id} 检测完成，检测到 {len(all_detections)} 个目标")
        if all_detections:
            logger.info(f"序列 {sequence_id} 检测结果前2个: "
                       f"{[(d.frame_id, d.bbox, d.confidence) for d in all_detections[:2]]}")
        
        return all_detections
    
    def detect_all_sequences(self, data_path: str, sample_ratio: float = 0.2, auto_discover: bool = True) -> List[DetectionResult]:
        """检测所有序列"""
        all_results = []

        # 如果启用自动发现，则使用发现的序列
        if auto_discover:
            discovered_sequences = self.discover_sequences(data_path)
            if discovered_sequences:
                sequences_to_process = discovered_sequences
                logger.info(f"使用自动发现的序列: {sequences_to_process}")
            else:
                sequences_to_process = self.target_sequences
                logger.info(f"未发现序列，使用默认序列: {sequences_to_process}")
        else:
            sequences_to_process = self.target_sequences
            logger.info(f"使用指定序列: {sequences_to_process}")

        for i, sequence_id in enumerate(sequences_to_process):
            logger.info(f"开始检测序列: {sequence_id} ({i+1}/{len(sequences_to_process)})")

            sequence_results = self.detect_sequence_with_temporal_window(
                data_path, sequence_id, sample_ratio
            )
            all_results.extend(sequence_results)
        
        logger.info(f"所有序列检测完成，总共检测到 {len(all_results)} 个目标")
        
        # 检测质量统计
        if all_results:
            confidences = [r.confidence for r in all_results]
            temporal_scores = [r.temporal_score for r in all_results]
            
            avg_confidence = np.mean(confidences)
            avg_temporal = np.mean(temporal_scores)
            high_conf_count = sum(1 for c in confidences if c >= 0.3)
            estimated_precision = high_conf_count / len(all_results)
            
            logger.info("检测质量统计:")
            logger.info(f"  平均置信度: {avg_confidence:.3f}")
            logger.info(f"  平均时序得分: {avg_temporal:.3f}")
            logger.info(f"  高置信度检测数量: {high_conf_count}/{len(all_results)}")
            logger.info(f"  估计精确率: {estimated_precision:.3f}")
            
            if estimated_precision >= 0.5:
                logger.info("✅ 精确率满足要求 (≥ 0.5)")
            else:
                logger.warning("⚠️ 精确率不足，建议调整检测参数")
        
        return all_results

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='BigGoal弱小目标检测')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--data_path', type=str, required=True, help='图像数据路径')
    parser.add_argument('--output_dir', type=str, default='output', help='输出目录')
    parser.add_argument('--sample_ratio', type=float, default=0.2, help='采样比例')
    parser.add_argument('--sequences', type=str, nargs='*', help='指定要处理的序列名称')
    parser.add_argument('--no_auto_discover', action='store_true', help='禁用自动发现序列')

    args = parser.parse_args()

    os.makedirs(args.output_dir, exist_ok=True)

    # 创建检测器
    target_sequences = args.sequences if args.sequences else None
    detector = BigGoalDetector(args.model_path, target_sequences=target_sequences)

    # 执行检测
    logger.info("开始检测...")
    auto_discover = not args.no_auto_discover
    detection_results = detector.detect_all_sequences(args.data_path, args.sample_ratio, auto_discover)
    
    # 保存检测结果
    results_file = os.path.join(args.output_dir, "biggoal_detection_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump([{
            'bbox': r.bbox,
            'label': r.label,
            'confidence': r.confidence,
            'frame_id': r.frame_id,
            'sequence_id': r.sequence_id,
            'temporal_score': r.temporal_score
        } for r in detection_results], f, ensure_ascii=False, indent=2)
    
    logger.info(f"检测结果已保存到: {results_file}")

if __name__ == "__main__":
    main()
