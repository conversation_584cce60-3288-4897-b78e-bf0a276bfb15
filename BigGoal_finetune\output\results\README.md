# BigGoal弱小目标检测结果

## 文件说明

### 主要结果文件
- `biggoal_final_report.json`: 最终整合报告，包含完整的评估指标和统计信息
- `biggoal_final_detections.json`: 最终检测结果，包含所有5个序列的检测数据

### 备份文件
- `processed_detection_results.json`: 处理后的检测结果（备份）
- `processed_detection_results_evaluation.json`: 处理后的评估结果（备份）

## 序列信息
- **训练序列**: data03, data05, data20, data21 (4个序列)
- **测试序列**: data22 (1个序列)
- **总序列数**: 5个序列

## 评估指标
- **精确率**: 检测正确的目标数 / 总检测数
- **召回率**: 检测正确的目标数 / 实际目标数  
- **F1分数**: 精确率和召回率的调和平均数
- **时序稳定性**: 相邻帧检测结果的一致性

## 备份目录
`backup/` 目录包含了所有中间处理文件和原始结果文件的备份。

## 使用说明
1. 查看 `biggoal_final_report.json` 获取完整的评估报告
2. 查看 `biggoal_final_detections.json` 获取详细的检测结果
3. 如需恢复中间文件，可从 `backup/` 目录中获取

生成时间: 2024-01-01
项目: BigGoal弱小目标检测系统
