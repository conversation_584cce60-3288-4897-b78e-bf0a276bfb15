#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BigGoal弱小目标检测LoRA微调脚本
针对5个序列的红外弱小目标检测优化
"""

import os
import json
import logging
import argparse
from dataclasses import dataclass, field
from typing import Optional, Dict, Any, List
import torch
from torch.utils.data import Dataset as TorchDataset
from transformers import (
    AutoTokenizer, 
    AutoProcessor,
    Qwen2_5_VLForConditionalGeneration,
    TrainingArguments,
    Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
import swanlab

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class ModelArguments:
    model_path: str = field(metadata={"help": "模型路径"})

@dataclass
class DataArguments:
    train_data: str = field(metadata={"help": "训练数据路径"})
    data_ratio: float = field(default=0.2, metadata={"help": "数据使用比例"})

@dataclass
class LoraArguments:
    lora_rank: int = field(default=16, metadata={"help": "LoRA rank"})
    lora_alpha: int = field(default=32, metadata={"help": "LoRA alpha"})
    lora_dropout: float = field(default=0.05, metadata={"help": "LoRA dropout"})

@dataclass
class TrainingArguments2(TrainingArguments):
    output_dir: str = field(metadata={"help": "输出目录"})
    num_train_epochs: int = field(default=3, metadata={"help": "训练轮数"})
    per_device_train_batch_size: int = field(default=1, metadata={"help": "批次大小"})
    learning_rate: float = field(default=1e-5, metadata={"help": "学习率"})
    warmup_steps: int = field(default=20, metadata={"help": "预热步数"})
    save_steps: int = field(default=50, metadata={"help": "保存频率"})
    logging_steps: int = field(default=5, metadata={"help": "日志频率"})

class BigGoalDataset(TorchDataset):
    """BigGoal弱小目标数据集"""
    
    def __init__(self, data_path: str, processor, tokenizer, data_ratio: float = 0.2):
        self.processor = processor
        self.tokenizer = tokenizer
        
        logger.info(f"加载训练数据: {data_path}")
        with open(data_path, 'r', encoding='utf-8') as f:
            all_data = json.load(f)
        
        # 使用指定比例的数据
        total_samples = len(all_data)
        use_samples = max(1, int(total_samples * data_ratio))
        self.data = all_data[:use_samples]
        
        # 验证数据质量
        self._validate_data()
        
        logger.info(f"总样本数: {total_samples}, 使用样本数: {len(self.data)} ({data_ratio*100:.1f}%)")
    
    def _validate_data(self):
        """验证数据质量，移除无效样本"""
        import re
        import os
        from PIL import Image
        
        valid_data = []
        invalid_count = 0
        
        for item in self.data:
            try:
                conversations = item["conversations"]
                human_msg = conversations[0]["value"]
                
                # 提取图像路径
                image_match = re.search(r'<\|vision_start\|>(.*?)<\|vision_end\|>', human_msg)
                if not image_match:
                    invalid_count += 1
                    continue
                
                image_path = image_match.group(1)
                
                # 检查文件是否存在
                if not os.path.exists(image_path):
                    invalid_count += 1
                    continue
                
                # 检查图像是否可以正常加载
                try:
                    with Image.open(image_path) as img:
                        if img.size[0] < 32 or img.size[1] < 32:
                            invalid_count += 1
                            continue
                except Exception:
                    invalid_count += 1
                    continue
                
                valid_data.append(item)
                
            except Exception:
                invalid_count += 1
                continue
        
        self.data = valid_data
        logger.info(f"数据验证完成: 有效样本 {len(valid_data)}, 无效样本 {invalid_count}")
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        conversations = item["conversations"]
        
        # 获取输入和输出
        human_msg = conversations[0]["value"]
        gpt_msg = conversations[1]["value"]
        
        # 提取图像路径
        import re
        import os
        image_match = re.search(r'<\|vision_start\|>(.*?)<\|vision_end\|>', human_msg)
        if image_match:
            image_path = image_match.group(1)
            text_prompt = human_msg.replace(f'<|vision_start|>{image_path}<|vision_end|>', '')
        else:
            raise ValueError(f"无法从消息中提取图像路径: {human_msg}")
        
        # 检查图像文件是否存在
        if not os.path.exists(image_path):
            logger.warning(f"图像文件不存在: {image_path}, 跳过样本 {idx}")
            return self.__getitem__((idx + 1) % len(self.data))
        
        # 处理图像和文本
        try:
            from PIL import Image
            image = Image.open(image_path).convert('RGB')
            
            # 检查图像尺寸
            if image.size[0] < 32 or image.size[1] < 32:
                logger.warning(f"图像尺寸过小: {image.size}, 路径: {image_path}")
                return self.__getitem__((idx + 1) % len(self.data))
            
            # 使用processor处理
            messages = [
                {
                    "role": "user",
                    "content": [
                        {"type": "image", "image": image},
                        {"type": "text", "text": text_prompt}
                    ]
                }
            ]
            
            # 处理输入
            text = self.processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            
            # 直接使用processor处理图像和文本
            inputs = self.processor(
                text=[text],
                images=[image],
                padding=True,
                return_tensors="pt"
            )
            
            # 检查图像处理结果
            if "pixel_values" not in inputs or inputs["pixel_values"].numel() == 0:
                logger.warning(f"图像处理失败，pixel_values为空: {image_path}")
                return self.__getitem__((idx + 1) % len(self.data))
            
            # 处理标签
            full_text = text + gpt_msg + self.tokenizer.eos_token
            full_inputs = self.tokenizer(
                full_text,
                return_tensors="pt",
                padding=True,
                truncation=True,
                max_length=2048
            )
            
            # 创建标签，只对输出部分计算损失
            labels = full_inputs["input_ids"].clone()
            input_len = len(inputs["input_ids"][0])
            labels[0, :input_len] = -100  # 忽略输入部分的损失
            
            return {
                "input_ids": full_inputs["input_ids"][0],
                "attention_mask": full_inputs["attention_mask"][0],
                "pixel_values": inputs["pixel_values"][0],
                "image_grid_thw": inputs["image_grid_thw"][0] if "image_grid_thw" in inputs else torch.tensor([1, 1, 1]),
                "labels": labels[0]
            }
            
        except Exception as e:
            logger.error(f"处理样本 {idx} 时出错: {e}, 图像路径: {image_path}")
            return self.__getitem__((idx + 1) % len(self.data))

class CustomDataCollator:
    """自定义数据收集器"""
    
    def __init__(self, tokenizer):
        self.tokenizer = tokenizer
    
    def __call__(self, features: List[Dict[str, Any]]) -> Dict[str, torch.Tensor]:
        # 过滤掉None值
        valid_features = [f for f in features if f is not None]
        
        if not valid_features:
            raise ValueError("所有样本都无效")
        
        # 分别处理不同类型的数据
        input_ids = [f["input_ids"] for f in valid_features]
        attention_mask = [f["attention_mask"] for f in valid_features]
        labels = [f["labels"] for f in valid_features]
        
        # 填充序列
        max_len = max(len(ids) for ids in input_ids)
        
        padded_input_ids = []
        padded_attention_mask = []
        padded_labels = []
        
        for i in range(len(valid_features)):
            ids = input_ids[i]
            mask = attention_mask[i]
            label = labels[i]
            
            # 填充到最大长度
            pad_len = max_len - len(ids)
            if pad_len > 0:
                ids = torch.cat([ids, torch.full((pad_len,), self.tokenizer.pad_token_id)])
                mask = torch.cat([mask, torch.zeros(pad_len)])
                label = torch.cat([label, torch.full((pad_len,), -100)])
            
            padded_input_ids.append(ids)
            padded_attention_mask.append(mask)
            padded_labels.append(label)
        
        batch = {
            "input_ids": torch.stack(padded_input_ids),
            "attention_mask": torch.stack(padded_attention_mask),
            "labels": torch.stack(padded_labels)
        }
        
        # 处理图像数据
        if "pixel_values" in valid_features[0] and valid_features[0]["pixel_values"] is not None:
            pixel_values = [f["pixel_values"] for f in valid_features]
            batch["pixel_values"] = torch.stack(pixel_values)
        
        if "image_grid_thw" in valid_features[0] and valid_features[0]["image_grid_thw"] is not None:
            image_grid_thw = [f["image_grid_thw"] for f in valid_features]
            batch["image_grid_thw"] = torch.stack(image_grid_thw)
        
        return batch

class CustomTrainer(Trainer):
    """自定义训练器"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    def compute_loss(self, model, inputs, return_outputs=False, num_items_in_batch=None):
        """计算损失"""
        outputs = model(**inputs)
        loss = outputs.loss
        return (loss, outputs) if return_outputs else loss

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='BigGoal弱小目标检测微调')
    parser.add_argument('--model_path', type=str, required=True, help='模型路径')
    parser.add_argument('--train_data', type=str, required=True, help='训练数据路径')
    parser.add_argument('--output_dir', type=str, required=True, help='输出目录')
    parser.add_argument('--data_ratio', type=float, default=0.2, help='数据使用比例')
    parser.add_argument('--num_epochs', type=int, default=3, help='训练轮数')
    parser.add_argument('--lora_rank', type=int, default=16, help='LoRA rank')
    parser.add_argument('--learning_rate', type=float, default=1e-5, help='学习率')
    parser.add_argument('--swanlab_project', type=str, default='BigGoal_Finetune', help='SwanLab项目名')
    parser.add_argument('--experiment_name', type=str, default='biggoal_lora', help='实验名称')
    
    args = parser.parse_args()
    
    logger.info("=== BigGoal弱小目标检测微调 ===")
    logger.info(f"模型路径: {args.model_path}")
    logger.info(f"训练数据: {args.train_data}")
    logger.info(f"数据使用比例: {args.data_ratio*100:.1f}%")
    logger.info(f"学习率: {args.learning_rate}")
    logger.info(f"LoRA rank: {args.lora_rank}")
    
    # 初始化SwanLab
    swanlab.init(
        project=args.swanlab_project,
        experiment_name=args.experiment_name,
        config={
            "model_path": args.model_path,
            "data_ratio": args.data_ratio,
            "num_epochs": args.num_epochs,
            "lora_rank": args.lora_rank,
            "learning_rate": args.learning_rate
        }
    )
    
    # 加载模型和处理器
    logger.info("加载模型和处理器...")
    model = Qwen2_5_VLForConditionalGeneration.from_pretrained(
        args.model_path,
        torch_dtype="auto",
        device_map="auto"
    )
    
    processor = AutoProcessor.from_pretrained(args.model_path)
    tokenizer = AutoTokenizer.from_pretrained(args.model_path)
    
    # 配置LoRA
    logger.info(f"配置LoRA (rank={args.lora_rank}, alpha={args.lora_rank*2}, dropout=0.05)...")
    lora_config = LoraConfig(
        task_type=TaskType.CAUSAL_LM,
        r=args.lora_rank,
        lora_alpha=args.lora_rank * 2,
        lora_dropout=0.05,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    )
    
    model = get_peft_model(model, lora_config)
    model.print_trainable_parameters()
    
    # 准备训练数据
    logger.info("准备训练数据...")
    train_dataset = BigGoalDataset(args.train_data, processor, tokenizer, args.data_ratio)
    
    # 训练参数
    training_args = TrainingArguments(
        output_dir=args.output_dir,
        num_train_epochs=args.num_epochs,
        per_device_train_batch_size=1,
        gradient_accumulation_steps=8,
        learning_rate=args.learning_rate,
        warmup_steps=20,
        save_steps=50,
        logging_steps=5,
        remove_unused_columns=False,
        dataloader_pin_memory=False,
        fp16=True,
        gradient_checkpointing=True,
    )
    
    # 数据整理器
    data_collator = CustomDataCollator(tokenizer=tokenizer)
    
    # 创建训练器
    trainer = CustomTrainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        data_collator=data_collator,
        tokenizer=tokenizer,
    )
    
    # 开始训练
    logger.info("开始训练...")
    trainer.train()
    
    # 保存模型
    trainer.save_model()
    logger.info(f"模型已保存到: {args.output_dir}")
    
    swanlab.finish()

if __name__ == "__main__":
    main()
