#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试检测结果文件格式
"""

import json
import sys
from collections import defaultdict

def debug_detection_results(results_file):
    """调试检测结果文件"""
    print(f"调试检测结果文件: {results_file}")
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        print(f"检测结果总数: {len(data)}")
        
        if not data:
            print("检测结果为空！")
            return
        
        # 显示第一个结果的格式
        print(f"第一个检测结果: {data[0]}")
        
        # 统计序列
        sequences = defaultdict(int)
        frames = defaultdict(set)
        labels = defaultdict(int)
        
        for item in data:
            seq_id = item.get('sequence_id', 'unknown')
            frame_id = item.get('frame_id', 'unknown')
            label = item.get('label', 'unknown')
            
            sequences[seq_id] += 1
            frames[seq_id].add(frame_id)
            labels[label] += 1
        
        print(f"\n序列统计:")
        for seq, count in sequences.items():
            print(f"  {seq}: {count} 个检测结果, {len(frames[seq])} 个帧")
        
        print(f"\n类别统计:")
        for label, count in labels.items():
            print(f"  {label}: {count} 个")
        
        # 检查必需字段
        required_fields = ['bbox', 'label', 'confidence', 'frame_id', 'sequence_id']
        missing_fields = []
        
        for field in required_fields:
            if field not in data[0]:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"\n⚠️ 缺少必需字段: {missing_fields}")
        else:
            print(f"\n✅ 所有必需字段都存在")
            
    except Exception as e:
        print(f"❌ 读取文件失败: {e}")

if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("用法: python debug_detection_results.py <检测结果文件>")
        sys.exit(1)
    
    debug_detection_results(sys.argv[1])
